import { IApplicationService, Result } from '@core/core';
import { UpdateSensorTriggerEntryDto } from '../dto/entry/update-sensor-trigger-entry.application.dto';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface';
import { userNotFoundException } from 'apps/api/src/user/application/exceptions/not-found';
import { triggerNotFoundException } from '../exceptions/not-found';
import { invalidTriggerTypeException } from '../exceptions/invalid-trigger-type';
import { sensorNotFoundException } from 'apps/api/src/sensor/application/exceptions/not-found';
import { actuatorNotFoundException } from 'apps/api/src/actuators/application/exceptions/not-found';
import { Trigger } from '@modules/modules/trigger/domain/trigger';
import { ComparisonTypesEnum } from '@modules/modules/trigger/domain/enum/comparison-types.enum';
import { UpdateSensorTriggerResponseDto } from '../dto/response/update-sensor-trigger-response.application'

export class UpdateSensorTriggerApplicationService
  implements IApplicationService<UpdateSensorTriggerEntryDto, UpdateSensorTriggerResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly triggerRepository: ITriggerRepository;
  private readonly sensorRepository: ISensorRepository;
  private readonly actuatorRepository: IActuatorRepository;

  constructor(
    triggerRepository: ITriggerRepository,
    userRepository: IUserRepository,
    sensorRepository: ISensorRepository,
    actuatorRepository: IActuatorRepository,
  ) {
    this.userRepository = userRepository;
    this.triggerRepository = triggerRepository;
    this.sensorRepository = sensorRepository;
    this.actuatorRepository = actuatorRepository;
  }

  async execute(updateTriggerEntry: UpdateSensorTriggerEntryDto): Promise<Result<UpdateSensorTriggerResponseDto>> {
    const triggerResult = await this.triggerRepository.findUserTriggerById(
      updateTriggerEntry.triggerId,
      updateTriggerEntry.userId
    );
    if (!triggerResult.isPresent()) {
      return Result.fail(triggerNotFoundException());
    }

    const existingTrigger = triggerResult.get();

    // Validate that this is a sensor trigger (has sensorId and no objectiveHour)
    if (existingTrigger.ObjectiveHour || !existingTrigger.SensorId) {
      return Result.fail(invalidTriggerTypeException());
    }

    // Validate sensor exists
    const sensorResult = await this.sensorRepository.findSensor(
      updateTriggerEntry.userId,
      updateTriggerEntry.sensorId
    );
    if (!sensorResult.isPresent()) {
      return Result.fail(sensorNotFoundException());
    }

    // Validate all actuators exist
    for (const actuator of updateTriggerEntry.actuators) {
      const actuatorResult = await this.actuatorRepository.findActuatorById(
        updateTriggerEntry.userId,
        actuator.actuatorId
      );
      if (!actuatorResult.isPresent()) {
        return Result.fail(actuatorNotFoundException());
      }
    }
    console.log(updateTriggerEntry.actuators);
    // Create updated trigger with new sensor data
    existingTrigger.update(
      updateTriggerEntry.name,
      existingTrigger.CreationDate,
      existingTrigger.UserId,
      updateTriggerEntry.comparison as ComparisonTypesEnum,
      updateTriggerEntry.actuators,
      updateTriggerEntry.objectiveMeasure,
      updateTriggerEntry.sensorId,
      undefined
    );

    // Update the trigger
    await this.triggerRepository.updateTrigger(existingTrigger);

    return Result.success<UpdateSensorTriggerResponseDto>({
      response: 'Sensor trigger updated successfully',
    });
  }

  get name(): string {
    return this.constructor.name;
  }
}
