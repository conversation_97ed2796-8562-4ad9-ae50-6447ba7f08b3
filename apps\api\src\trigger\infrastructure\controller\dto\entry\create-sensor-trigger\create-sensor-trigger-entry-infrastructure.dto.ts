import { SensorTypesEnum } from '@modules/modules/sensor/domain/enums/sensor-types.enum';
import { ActionTypesEnum } from '@modules/modules/trigger/domain/enum/action-types.enum'
import { ComparisonTypesEnum } from '@modules/modules/trigger/domain/enum/comparison-types.enum'
import { IActuatorAction } from '@modules/modules/trigger/domain/types/actuator-action.type'
import { ApiProperty } from '@nestjs/swagger';
import { ArrayMaxSize, IsArray, IsEnum, IsInt, IsObject, IsOptional, IsString, Max, MaxLength, Min } from 'class-validator';

export class createSensorTriggerEntryInfraestructureDto {

  @ApiProperty({
    required: false,
    example: 'Trigger 1',
  })
  @IsString()
  name: string;

  @ApiProperty({
    required: false,
    example: '425169e1-e2ce-43f0-ab60-864500b32da9',
  })
  @IsString()
  @IsOptional()
  sensorId?: string;

  @ApiProperty({
    required: false,
    example: [25, 30],
  })
  @IsInt({ each: true })
  @IsOptional()
  @IsArray()
  objectiveMeasure?: number[];
  
  @ApiProperty({
    required: true,
    example: ComparisonTypesEnum.g,
  })
  @IsEnum(ComparisonTypesEnum)
  comparison: ComparisonTypesEnum;

  @ApiProperty({
    required: true,
    example: [{
      actuatorId: '425169e1-e2ce-43f0-ab60-864500b32da9',
      action: ActionTypesEnum.open,
    },
    {
      actuatorId: '425169e1-e2ce-43f0-ab60-864500b32da9',
      action: ActionTypesEnum.close,
    }],
  })
  @IsArray()
  actuators: IActuatorAction[];

  @ApiProperty({
    required: false,
    example: 30,
  })
  @IsInt()
  @IsOptional()
  @Min(0)
  @Max(59)
  ObjectiveMinute?: number;

  @ApiProperty({
    required: false,
    example: 15,
  })
  @IsInt()
  @IsOptional()
  @Min(0)
  @Max(23)
  ObjectiveHour?: number;

  @ApiProperty({
    required: false,
    example: [1,2,4],
  })
  @IsArray()
  @IsInt({ each: true })
  @Min(1, { each: true })
  @Max(7, { each: true })
  @ArrayMaxSize(7)
  @IsOptional()
  ObjectiveDays?: number[];

}
