export type Exception<T = any> = {
  code: string;
  message: string;
  info?: T;
};

export const makeExceptionFactory = <T>(data: { code: string; message: string }) => {
  const target = class extends Error implements Exception<T> {
    code = data.code;
    message = data.message;
    constructor(public info?: T) {
      super();
      const arr = this.stack?.split('\n');
      arr?.splice(1, 1);
      this.stack = arr?.join('\n');
    }
  };
  return (info?: T) => new target(info);
};
