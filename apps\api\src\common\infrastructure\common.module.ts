import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CommonController } from './controller/common.controller';
import { EmqxApiService } from './services/emqx-api.service';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { AuthModule } from '../../auth/infrastructure/auth.module'

@Module({
  imports: [
    HttpModule,
    UserEntityModule,
    AuthModule
  ],
  controllers: [CommonController],
  providers: [EmqxApiService],
  exports: [EmqxApiService],
})
export class CommonModule {}
