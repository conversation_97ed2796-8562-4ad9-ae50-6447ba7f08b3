version: '3.8'
services:

  mongoDb:
    image: mongo:5
    container_name: mongo_tank_monitor
    restart: always
    ports:
      - ${MONGO_PORT}:27017
    environment:
      MONGODB_DATABASE: tank-monitor-db
    volumes:
      - mongo_data:/data/db
    networks:
      - tank-monitor-network

  emqx1:
    image: emqx/emqx-enterprise:5.9.0
    container_name: emqx1
    environment:
    - "EMQX_NODE_NAME=<EMAIL>"
    - "EMQX_CLUSTER__DISCOVERY_STRATEGY=static"
    - "EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>,<EMAIL>]"
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx", "ctl", "status"]
      interval: 5s
      timeout: 25s
      retries: 5
    networks:
      emqx-bridge:
        aliases:
        - node1.emqx.com
    ports:
      - 1883:1883
      - 8083:8083
      - 8084:8084
      - 8883:8883
      - 18083:18083
    # volumes:
    #   - $PWD/emqx1_data:/opt/emqx/data

  emqx2:
    image: emqx/emqx-enterprise:5.9.0
    container_name: emqx2
    environment:
    - "EMQX_NODE_NAME=<EMAIL>"
    - "EMQX_CLUSTER__DISCOVERY_STRATEGY=static"
    - "EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>,<EMAIL>]"
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx", "ctl", "status"]
      interval: 5s
      timeout: 25s
      retries: 5
    networks:
      emqx-bridge:
        aliases:
        - node2.emqx.com
    # volumes:
    #   - $PWD/emqx2_data:/opt/emqx/data

networks:
  emqx-bridge:
    driver: bridge
  tank-monitor-network:
    driver: bridge

volumes:
  mongo_data: