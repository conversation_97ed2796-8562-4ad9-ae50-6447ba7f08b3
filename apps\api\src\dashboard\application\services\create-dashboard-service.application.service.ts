import { IApplicationService, Result, IdGenerator, EncryptorBcrypt } from '@core/core';

import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { userNotFoundException } from 'apps/api/src/user/application/exceptions/not-found';
import { IDashboardRepository } from '@modules/modules/dashboard/domain/repository/dashboard-repository.interface';
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface';
import { CreateDashboardEntryDto } from '../dto/entry/create-dashboard-entry.application.dto';
import { CreateDashboardResponseDto } from '../dto/response/create-dashboard-response.application.dto';
import { sensorNotFoundException } from 'apps/api/src/sensor/application/exceptions/not-found';
import { Dashboard } from '@modules/modules/dashboard/domain/dashboard';

export class CreateDashboardApplicationService
  implements IApplicationService<CreateDashboardEntryDto, CreateDashboardResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly uuidGenerator: IdGenerator<string>;
  private readonly dashboardRepository: IDashboardRepository;
  private readonly sensorRepository: ISensorRepository;

  constructor(
    sensorRepository: ISensorRepository,
    dashboardRepository: IDashboardRepository,
    userRepository: IUserRepository,
    uuidGenerator: IdGenerator<string>,
  ) {
    this.userRepository = userRepository;
    this.uuidGenerator = uuidGenerator;
    this.dashboardRepository = dashboardRepository;
    this.sensorRepository = sensorRepository;
  }

  async execute(createDashboardEntry: CreateDashboardEntryDto): Promise<Result<CreateDashboardResponseDto>> {
    const findResult = await this.userRepository.findUserById(createDashboardEntry.userId);

    if (!findResult.isPresent()) return Result.fail(userNotFoundException());

    const findSensorResult = await this.sensorRepository.findSensor(
      createDashboardEntry.userId,
      createDashboardEntry.sensorId,
    );

    if (!findSensorResult.isPresent()) return Result.fail(sensorNotFoundException());
    
    const idDashboard = await this.uuidGenerator.generateId();

    const create = Dashboard.create(
      idDashboard,
      createDashboardEntry.sensorId,
      createDashboardEntry.position,
      createDashboardEntry.userId,
      createDashboardEntry.type,
    );

    await this.dashboardRepository.saveDashboard(create);
    
    return Result.success({
      id: create.Id,
      sensorId: create.SensorId,
      userId: create.SensorId,
      type: create.Type,
      position: create.Position
    });
  }

  get name(): string {
    return this.constructor.name;
  }
}
