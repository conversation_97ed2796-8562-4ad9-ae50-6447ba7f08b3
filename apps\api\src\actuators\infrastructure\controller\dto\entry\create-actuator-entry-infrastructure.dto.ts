import { ActuatorTypesEnum } from '@modules/modules/actuator/domain/enums/actuator-types.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';

export class CreateActuatorEntryInfraestructureDto {
  @ApiProperty({
    required: false,
    example: 'Actuator 1',
  })
  @IsString()
  name: string;

  @ApiProperty({
    required: false,
    example: 'valve',
  })
  @IsEnum(ActuatorTypesEnum)
  type: ActuatorTypesEnum;
}
