import { Body, Controller, Post, Logger, Inject, Get, Query, Param, ParseU<PERSON><PERSON>ipe, Delete, Put } from '@nestjs/common';

import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { UseGuards } from '@nestjs/common/decorators/core/use-guards.decorator';

import {
  ExceptionDecorator,
  GetUser,
  HttpExceptionH<PERSON>ler,
  IdGenerator,
  JwtAuthGuard,
  LoggingDecorator,
  NativeLogger,
  PaginationDto,
  PerformanceDecorator,
  UuidGenerator,
} from '@core/core';
import * as bcrypt from 'bcryptjs';
import { User } from '@modules/modules/user/domain/user';
import { NestEventEmitter } from '@core/core/infrastructure/event-emitter/nest-event-emitter';
import { paginationResponseHandler } from '@core/core/infrastructure/pagination/helpers/pagination.response-handler';
import { OdmActuatorRepository } from '@modules/modules/actuator/infrastructure/repositories/odm-repositories/odm-actuator-repository';
import { FindUserActuatorsApplicationService } from '../../application/services/find-user-actuators.service';
import { createActuatorSwaggerResponseDto } from './dto/response/create-actuator-swagger-response.dto';
import { CreateActuatorEntryInfraestructureDto } from './dto/entry/create-actuator-entry-infrastructure.dto';
import { CreateActuatorApplicationService } from '../../application/services/create-actuator.application.service';
import { TenAlphanumericCodeGenerator } from '@core/core/infrastructure/code-generator/ten-alphanumeric-code-generator';
import { OdmActuatorTokenRepository } from '@modules/modules/actuator/infrastructure/repositories/odm-repositories/odm-actuator-token-repository';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository'
import { actuatorNotFoundException } from '../../application/exceptions/not-found'
import { makeExceptionFactory } from '@core/core/domain/exception/exception'
import { FindOneActuatorApplicationService } from '../../application/services/find-one/find-one-actuator.service'
import { MqttService } from '@core/core/infrastructure/mqtt/services/mqtt.service'
import { DeleteActuatorApplicationService } from '../../application/services/delete-actuator.application.service'
import { UpdateActuatorNameApplicationService } from '../../application/services/update-actuator-name.application.service';
import { UpdateActuatorNameEntryInfrastructureDto } from './dto/entry/update-actuator-name-entry-infrastructure.dto';
import { UpdateActuatorNameSwaggerResponseDto } from './dto/response/update-actuator-name-swagger-response.dto';

@ApiTags('Actuator')
@Controller('actuator')
export class ActuatorController {
  private readonly logger: Logger;
  private readonly uuidGenerator: IdGenerator<string>;

  constructor(
    @Inject() private readonly eventEmitter: NestEventEmitter,
    private readonly userRepository: OdmUserRepository,
    private readonly actuatorRepository: OdmActuatorRepository,
    private readonly actuatorTokenRepository: OdmActuatorTokenRepository,
    @Inject() private readonly messageSender: MqttService,
  ) {
    this.logger = new Logger('ActuatorController');
    this.uuidGenerator = new UuidGenerator();
  }

  @Get('many')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findUserActuators(@GetUser() user: User, @Query() query: PaginationDto) {
    const result = await new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindUserActuatorsApplicationService(this.actuatorRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      userId: user.Id,
      page: query.page,
      limit: query.limit,
    });

    const pagination = await this.actuatorRepository.findManyPagination(user.Id, query.page, query.limit);

    return paginationResponseHandler({
      response: result.Value!,
      pagination,
    });
  }

  @Post('add-actuator-user')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ description: 'Añadir un actuator a un usuario', type: createActuatorSwaggerResponseDto })
  @ApiBearerAuth()
  async addActuatorToUser(@GetUser() user: User, @Body() createActuatorDto: CreateActuatorEntryInfraestructureDto) {
    const createActuatorApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new CreateActuatorApplicationService(this.uuidGenerator, this.eventEmitter),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );

    const result = await createActuatorApplicationService.execute({
      userId: user.Id,
      name: createActuatorDto.name,
      type: createActuatorDto.type,
    });

    const actuatorId = result.Value.actuatorCreatedEvent.actuator.Id;

    const actuatorToken = await bcrypt.hash(TenAlphanumericCodeGenerator.generate(), 3);

    await this.actuatorTokenRepository.store({
      actuatorId,
      token: actuatorToken,
      userId: user.Id,
    });

    return { actuator: result.Value.actuatorCreatedEvent.actuator, actuatorToken };
  }

  @Get('one/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findOneActuator(@GetUser() user: User, @Param('id', ParseUUIDPipe) id: string) {
    const result = await new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindOneActuatorApplicationService(this.userRepository, this.actuatorRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      actuatorId: id,
      userId: user.Id,
    });

    return result.Value;
  }

  @Get('token/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getActuatorToken(@GetUser() user: User, @Param('id', ParseUUIDPipe) id: string) {
    const exceptionHandler = new HttpExceptionHandler();

    const actuator = user.Actuators?.find((s) => s.Id === id);

    if (!actuator) {
      exceptionHandler.HandleException(actuatorNotFoundException());
    }

    const tokenResult = await this.actuatorTokenRepository.getToken(id);

    if (!tokenResult.isPresent()) {
      const tokenNotFoundException = makeExceptionFactory({
        code: 'ST-NF',
        message: 'Token not found',
      });
      exceptionHandler.HandleException(tokenNotFoundException());
    }

    return {
      token: tokenResult.get(),
    };
  }

  @Delete('delete/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Eliminar un actuator', type: String })
  async deleteActuator(@GetUser() user: User, @Param('id', ParseUUIDPipe) id: string) {
    const deleteApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new DeleteActuatorApplicationService(this.userRepository, this.messageSender, this.actuatorRepository, this.eventEmitter),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    const result = await deleteApplicationService.execute({
      userId: user.Id,
      actuatorId: id,
    });

    return result.Value;
  }

  @Put('update-name/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Update actuator name', type: UpdateActuatorNameSwaggerResponseDto })
  async updateActuatorName(
    @GetUser() user: User,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateActuatorDto: UpdateActuatorNameEntryInfrastructureDto
  ) {
    const updateApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new UpdateActuatorNameApplicationService(this.userRepository, this.actuatorRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );

    const result = await updateApplicationService.execute({
      userId: user.Id,
      actuatorId: id,
      name: updateActuatorDto.name,
    });

    return result.Value;
  }
}
