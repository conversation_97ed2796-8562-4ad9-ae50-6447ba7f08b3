import { Inject, Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketGateway,
} from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { JwtPayload } from '@core/core/infrastructure/jwt/decorator/dto/jwt-payload.interface';
import { JwtService } from '@nestjs/jwt';
import { MqttService } from '@core/core/infrastructure/mqtt/services/mqtt.service'

@WebSocketGateway(4002, { cors: true })
export class MeasurementGateway implements OnGatewayConnection, OnGatewayDisconnect {
  private readonly logger = new Logger('UserGateway');

  constructor(
    private readonly jwtService: JwtService,
    @Inject() private readonly mqttService: MqttService,
    
  ) {}

  async handleConnection(client: Socket) {
    this.logger.log(`Client ${client.id} connected`);

    const token = client.handshake.headers.authorization as string;
    let payload: JwtPayload;

    try {
      payload = this.jwtService.verify(token);
    } catch {
      this.logger.warn(`Client ${client.id} disconnected due to invalid JWT`);
      client.emit('invalid-jwt');
      client.disconnect();
      return;
    }

  }

  handleDisconnect(client: Socket) {
    this.logger.warn(`Client ${client.id} disconnected`);
  }

  @SubscribeMessage('get-actuators-status')
  async handleGetActuatorsStatus(@ConnectedSocket() client: Socket) {



    client.emit('actuators-status', actuatorsStatusList);
  }
}
