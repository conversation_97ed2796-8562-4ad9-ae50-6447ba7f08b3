import { Inject, Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketGateway,
} from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { MqttService } from '@core/core/infrastructure/mqtt/services/mqtt.service';
import {
  ExceptionDecorator,
  HttpExceptionHandler,
  LoggingDecorator,
  NativeLogger,
  PerformanceDecorator,
} from '@core/core';
import { ExecuteActionActuatorService } from '../../application/services/execute-action-actuator.service';
import { OdmActuatorRepository } from '@modules/modules/actuator/infrastructure/repositories/odm-repositories/odm-actuator-repository';
import { ActionTypesEnum } from '@modules/modules/trigger/domain/enum/action-types.enum'

@WebSocketGateway({ 
  path: '/actuator-ws', 
  cors: {
    origin: [`${process.env.FRONTEND_URL}`],
    methods: ['GET', 'POST']
  },
  transports: ['websocket'],
  allowUpgrades: true, })
export class ActuatorGateway implements OnGatewayConnection, OnGatewayDisconnect {
  private readonly logger = new Logger('ActuatorGateway');

  constructor(
    private readonly jwtService: JwtService,
    @Inject() private readonly mqttService: MqttService,
    private readonly actuatorRepository: OdmActuatorRepository,
  ) {}

  async handleConnection(client: Socket) {
    this.logger.log(`Client ${client.id} connected`);

    const token = client.handshake.headers.authorization as string;

    try {
      this.jwtService.verify(token);
    } catch {
      this.logger.warn(`Client ${client.id} disconnected due to invalid JWT`);
      client.emit('invalid-jwt');
      client.disconnect();
      return;
    }
    this.mqttService.subscribe(`actuator-open/#`, (message, receivedTopic) => {
      client.emit('actuator-open', JSON.parse(message));
    });
  }

  handleDisconnect(client: Socket) {
    this.logger.warn(`Client ${client.id} disconnected`);
  }

  @SubscribeMessage('get-actuator-open')
  async handleGetActuatorsStatus(@ConnectedSocket() client: Socket, @MessageBody() data: { actuatorId: string }) {
    const lastMessage = await this.mqttService.getLastMessage(`actuator-open/${data.actuatorId}`);

    client.emit('actuator-open', JSON.parse(lastMessage));
  }

  @SubscribeMessage('toggle-actuator')
  async handleToggleActuator(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { actuatorId: string; action: ActionTypesEnum, userId: string },
  ) {
    const service = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new ExecuteActionActuatorService(this.actuatorRepository, this.mqttService),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    await service.execute({
      userId: data.userId,
      actuatorId: data.actuatorId,
      action: data.action,
    });
  }
}
