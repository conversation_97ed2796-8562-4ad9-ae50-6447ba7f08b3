import { IApplicationService, Result } from '@core/core';
import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { FindUserTriggersEntryDto } from '../dto/entry/find-user-triggers.application.dto';
import { FindUserTriggersResponseDto } from '../dto/response/find-user-triggers-response.application.dto';

export class FindUserTriggersApplicationService
  implements IApplicationService<FindUserTriggersEntryDto, FindUserTriggersResponseDto[]>
{
  constructor(private readonly triggersRepository: ITriggerRepository) {}

  async execute(data: FindUserTriggersEntryDto): Promise<Result<FindUserTriggersResponseDto[]>> {
    const triggers = await this.triggersRepository.getAllUserTriggers(data.userId, data.page, data.limit);

    return Result.success(
      triggers.map((trigger) => {
        return {
          id: trigger.Id,
          name: trigger.Name,
          creationDate: trigger.CreationDate,
          objectiveMeasure: trigger.ObjectiveMeasure,
          sensorId: trigger.SensorId,
          userId: trigger.UserId,
          comparison: trigger.Comparison,
          actuators: trigger.Actuators,
          objectiveHour: trigger.ObjectiveHour,
        };
      }),
    );
  }
  get name(): string {
    return this.constructor.name;
  }
}
