import { IApplicationService, Result } from '@core/core';
import { FindOneActuatorEntryDto } from './dto/entry';
import { FindOneActuatorResponseDto } from './dto/response';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { userNotFoundException } from 'apps/api/src/user/application/exceptions/not-found';
import { actuatorNotFoundException } from '../../exceptions/not-found';

export class FindOneActuatorApplicationService
  implements IApplicationService<FindOneActuatorEntryDto, FindOneActuatorResponseDto>
{
  constructor(
    private readonly userRepository: IUserRepository,
    private readonly actuatorRepository: IActuatorRepository,
  ) {}

  async execute(data: FindOneActuatorEntryDto): Promise<Result<FindOneActuatorResponseDto>> {
    const { actuatorId, userId } = data;

    const userResult = await this.userRepository.findUserById(userId);
    if (!userResult.isPresent()) {
      return Result.fail(userNotFoundException());
    }

    const actuatorResult = await this.actuatorRepository.findActuatorById(userId, actuatorId);

    if (!actuatorResult.isPresent()) {
      return Result.fail(actuatorNotFoundException());
    }

    const actuator = actuatorResult.get();

    return Result.success({
      id: actuator.Id,
      name: actuator.Name,
      type: actuator.Type,
    });
  }
  get name(): string {
    return this.constructor.name;
  }
}
