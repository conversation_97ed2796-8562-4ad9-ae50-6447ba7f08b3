import {
  ExceptionDecorator,
  GetUser,
  HttpExceptionHandler,
  IdGenerator,
  IPushSender,
  JwtAuthGuard,
  LoggingDecorator,
  NativeLogger,
  PaginationDto,
  PerformanceDecorator,
  UuidGenerator,
} from '@core/core';
import { OdmActuatorRepository } from '@modules/modules/actuator/infrastructure/repositories/odm-repositories/odm-actuator-repository';
import { OdmSensorRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-sensor-repository';
import { OdmTriggerRepository } from '@modules/modules/trigger/infrastructure/repositories/odm-repositories/odm-trigger-repository';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { Body, Controller, Delete, Get, Inject, Logger, OnModuleInit, Param, ParseUUIDPipe, <PERSON>, Query, UseGuards } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON>erAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { CreateSensorTriggerApplicationService } from '../../application/services/create-sensor-trigger-service.application.service';
import { createSensorTriggerSwaggerResponseDto } from './dto/response/create-sensor-trigger-swagger-response.dto';
import { CreateSensorTriggerEntryDto } from '../../application/dto/entry/create-sensor-trigger-entry.application.dto';
import { User } from '@modules/modules/user/domain/user';
import { createSensorTriggerEntryInfraestructureDto } from './dto/entry/create-sensor-trigger/create-sensor-trigger-entry-infrastructure.dto';
import { console } from 'inspector';
import { CreateTimeTriggerEntryDto } from '../../application/dto/entry/create-time-trigger-entry.application.dto';
import { CreateTimeTriggerApplicationService } from '../../application/services/create-time-trigger-service.application.service';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { paginationResponseHandler } from '@core/core/infrastructure/pagination/helpers/pagination.response-handler';
import { FindUserTriggersApplicationService } from '../../application/services/find-user-triggers.service';
import { IActuatorAction } from '@modules/modules/trigger/domain/types/actuator-action.type';
import { MqttService } from '@core/core/infrastructure/mqtt/services/mqtt.service';
import { ExecuteActionActuatorService } from 'apps/api/src/actuators/application/services/execute-action-actuator.service';
import { FirebaseNotifier } from '@core/core/infrastructure/notifier/firebase-notifier-singleton';
import { SendPushUserInfraService } from '@modules/modules/common/infrastructure/services/send-push-user.service';
import { DeleteTriggerApplicationService } from '../../application/services/delete-trigger-service.application.service'

@ApiTags('Trigger')
@Controller('trigger')
export class TriggerController implements OnModuleInit {
  private readonly logger: Logger;
  private readonly uuidGenerator: IdGenerator<string>;
  private notifier: IPushSender;

  constructor(
    private readonly triggerRepository: OdmTriggerRepository,
    private readonly actuatorRepository: OdmActuatorRepository,
    private readonly userRepository: OdmUserRepository,
    private readonly sensorRepository: OdmSensorRepository,
    private schedulerRegistry: SchedulerRegistry,
    @Inject() private readonly mqttService: MqttService,
  ) {
    this.logger = new Logger('TriggerController');
    this.uuidGenerator = new UuidGenerator();
    this.notifier = FirebaseNotifier.getInstance();
  }

  async onModuleInit() {
    const triggers = await this.triggerRepository.getAllTriggers(1, 0);
    for (const trigger of triggers) {
      if (trigger.ObjectiveHour != undefined) {
        this.logger.log('Cron job created for ' + trigger.ObjectiveHour);
        this.createCronJob(trigger.ObjectiveHour, trigger.Id, trigger.Actuators, trigger.UserId, trigger.Name);
      }
    }
  }

  @Post('create-trigger')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ description: 'Añadir un sensor a un usuario', type: createSensorTriggerSwaggerResponseDto })
  @ApiBearerAuth()
  async createSensorTrigger(
    @GetUser() user: User,
    @Body() createSensorTriggerEntryDto: createSensorTriggerEntryInfraestructureDto,
  ) {
    if (
      createSensorTriggerEntryDto.ObjectiveHour == undefined &&
      createSensorTriggerEntryDto.ObjectiveHour == undefined &&
      createSensorTriggerEntryDto.ObjectiveDays == undefined
    ) {
      const createSensorTriggerEntry: CreateSensorTriggerEntryDto = {
        name: createSensorTriggerEntryDto.name,
        objectiveMeasure: createSensorTriggerEntryDto.objectiveMeasure!!,
        sensorId: createSensorTriggerEntryDto.sensorId!!,
        userId: user.Id,
        comparison: createSensorTriggerEntryDto.comparison,
        actuators: createSensorTriggerEntryDto.actuators,
      };

      const createSensorTriggerApplicationService = new ExceptionDecorator(
        new LoggingDecorator(
          new PerformanceDecorator(
            new CreateSensorTriggerApplicationService(
              this.sensorRepository,
              this.triggerRepository,
              this.userRepository,
              this.uuidGenerator,
              this.actuatorRepository,
            ),
            new NativeLogger(this.logger),
          ),
          new NativeLogger(this.logger),
        ),
        new HttpExceptionHandler(),
      );

      return await createSensorTriggerApplicationService.execute(createSensorTriggerEntry);
    } else if (
      createSensorTriggerEntryDto.objectiveMeasure == undefined &&
      createSensorTriggerEntryDto.sensorId == undefined
    ) {
      const createTimeTriggerEntry: CreateTimeTriggerEntryDto = {
        userId: user.Id,
        name: createSensorTriggerEntryDto.name,
        comparison: createSensorTriggerEntryDto.comparison,
        actuators: createSensorTriggerEntryDto.actuators,
        objectiveHour: createSensorTriggerEntryDto.ObjectiveHour!!,
        objectiveMinute: createSensorTriggerEntryDto.ObjectiveMinute!!,
        objectiveDays: createSensorTriggerEntryDto.ObjectiveDays!!,
      };

      const createTimeTriggerApplicationService = new ExceptionDecorator(
        new LoggingDecorator(
          new PerformanceDecorator(
            new CreateTimeTriggerApplicationService(
              this.triggerRepository,
              this.userRepository,
              this.uuidGenerator,
              this.actuatorRepository,
            ),
            new NativeLogger(this.logger),
          ),
          new NativeLogger(this.logger),
        ),
        new HttpExceptionHandler(),
      );

      const result = await createTimeTriggerApplicationService.execute(createTimeTriggerEntry);

      this.createCronJob(
        result.Value!.trigger.ObjectiveHour!,
        result.Value!.trigger.Id,
        result.Value!.trigger.Actuators,
        user.Id,
        result.Value!.trigger.Name,
      );
      return result;
    } else {
      return {
        statusCode: 400,
        message: 'No se puede crear un trigger con ambos tipos de entrada',
      };
    }
  }

  @Get('many')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findUserTriggers(@GetUser() user: User, @Query() query: PaginationDto) {
    const result = await new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindUserTriggersApplicationService(this.triggerRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      userId: user.Id,
      page: query.page,
      limit: query.limit,
    });
    console.log(result.Value);
    const pagination = await this.triggerRepository.findManyPagination(user.Id, query.page, query.limit);

    return paginationResponseHandler({
      response: result.Value!,
      pagination,
    });
  }

  @Delete('delete/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Eliminar un trigger', type: String })
  async deleteTrigger(@GetUser() user: User, @Param('id', ParseUUIDPipe) id: string) {
    const deleteApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new DeleteTriggerApplicationService(this.triggerRepository, this.userRepository, this.uuidGenerator, this.actuatorRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    const result = await deleteApplicationService.execute({
      userId: user.Id,
      triggerId: id,
    });

    if (result.Value.timeTrigger) {
      this.deleteCronJob(id);
    }

    return result.Value;
  }

  private createCronJob(
    cronString: string,
    jobName: string,
    actuatorActions: IActuatorAction[],
    userId: string,
    triggerName: string,
  ) {
    const job = new CronJob(
      cronString,
      () => {
        this.logger.log('Ejecutando trigger ' + jobName);
        const service = new ExceptionDecorator(
          new LoggingDecorator(
            new PerformanceDecorator(
              new ExecuteActionActuatorService(this.actuatorRepository, this.mqttService),
              new NativeLogger(this.logger),
            ),
            new NativeLogger(this.logger),
          ),
          new HttpExceptionHandler(),
        );
        let failed = false;
        actuatorActions.forEach(async (action) => {
          const result = await service.execute({
            userId: userId,
            action: action.action,
            actuatorId: action.actuatorId,
          });
          if (!result.isSuccess()) {
            failed = true;
          }
        });
        if (failed) {
          this.sendNotification(
            userId,
            'Trigger fallido',
            'El trigger ' + triggerName + ' ha fallado, por favor revise sus actuadores',
          );
        } else {
          this.sendNotification(userId, 'Trigger ejecutado!', 'Su trigger ' + triggerName + ' ha sido ejecutado!');
        }
      },
      null,
      null,
      'America/Caracas',
    );

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
    this.logger.log('Cron job created for time: ' + job.nextDate());
  }

  private deleteCronJob(jobName: string) {
    this.schedulerRegistry.deleteCronJob(jobName);
  }

  private async sendNotification(userId: string, title: string, body: string) {
    const service = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new SendPushUserInfraService(this.userRepository, this.notifier),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    try {
      await service.execute({
        userId: userId,
        title: title,
        body: body,
        eventType: 'trigger_executed_notification',
      });
    } catch (e) {
      this.logger.error(e);
    }
  }
}
