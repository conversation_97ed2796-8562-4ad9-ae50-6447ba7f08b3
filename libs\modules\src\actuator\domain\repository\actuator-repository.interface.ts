import { Optional } from '@core/core/domain/optional/optional';
import { Actuator } from '../actuator'
import { PaginationInfo } from '@core/core/infrastructure/pagination/interfaces/pagination.info'

export interface IActuatorRepository {
  findActuatorById(userId: string, actuatorId: string): Promise<Optional<Actuator>>;
  findActuatorsByUserId(userId: string, page: number, limit: number): Promise<Actuator[]>;
  findManyPagination(userId: string, page: number, limit: number): Promise<PaginationInfo>;
}
