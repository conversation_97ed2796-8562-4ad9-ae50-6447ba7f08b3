import { PaginationInfo } from '@core/core/infrastructure/pagination/interfaces/pagination.info'
import { Trigger } from '../trigger';
import { Optional } from '@core/core/domain/optional/optional'

export interface ITriggerRepository {
  save(trigger: Trigger): Promise<void>;
  getAllUserTriggers(userId: string, page: number, limit: number): Promise<Trigger[]>;
  getAllSensorTriggers(sensorId: string): Promise<Trigger[]>;
  getAllTriggers(page: number, limit: number): Promise<Trigger[]>;
  findManyPagination(userId: string, page: number, limit: number): Promise<PaginationInfo>;
  findUserTriggerById(triggerId: string, userId: string): Promise<Optional<Trigger>>;
  deleteTrigger(triggerId: string): Promise<void>;
  updateTrigger(trigger: Trigger): Promise<void>;
  deleteSensorTriggers(sensorId: string, userId: string): Promise<void>;
  deleteActuatorFromTriggers(actuatorId: string, userId: string): Promise<void>;
}
