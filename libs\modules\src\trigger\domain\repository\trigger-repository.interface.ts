import { PaginationInfo } from '@core/core/infrastructure/pagination/interfaces/pagination.info'
import { Trigger } from '../trigger';

export interface ITriggerRepository {
  save(trigger: Trigger): Promise<void>;
  getAllUserTriggers(userId: string, page: number, limit: number): Promise<Trigger[]>;
  getAllSensorTriggers(sensorId: string): Promise<Trigger[]>;
  getAllTriggers(page: number, limit: number): Promise<Trigger[]>;
  findManyPagination(userId: string, page: number, limit: number): Promise<PaginationInfo>;
}
