import { IApplicationService, Result } from '@core/core';
import { UpdateActuatorNameEntryDto } from '../dto/entry/update-actuator-name.application.dto';
import { UpdateActuatorNameResponseDto } from '../dto/response/update-actuator-name-response.application.dto';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface';
import { actuatorNotFoundException } from '../exceptions/not-found';
import { actuatorNameInUseException } from 'apps/api/src/user/application/exceptions/actuator-name-in-use'

export class UpdateActuatorNameApplicationService
  implements IApplicationService<UpdateActuatorNameEntryDto, UpdateActuatorNameResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly actuatorRepository: IActuatorRepository;

  constructor(userRepository: IUserRepository, actuatorRepository: IActuatorRepository) {
    this.userRepository = userRepository;
    this.actuatorRepository = actuatorRepository;
  }

  async execute(updateActuatorDto: UpdateActuatorNameEntryDto): Promise<Result<UpdateActuatorNameResponseDto>> {
    // Validate that the actuator exists
    const actuator = await this.actuatorRepository.findActuatorById(updateActuatorDto.userId, updateActuatorDto.actuatorId);
    if (!actuator.isPresent()) {
      return Result.fail(actuatorNotFoundException());
    }

    // Check if the new name is already in use by another actuator
    const nameInUse = await this.userRepository.userHasActuatorByName(updateActuatorDto.userId, updateActuatorDto.name);
    if (nameInUse) {
      // Check if it's the same actuator (allow updating with the same name)
      const existingActuator = actuator.get();
      if (existingActuator.Name !== updateActuatorDto.name) {
        return Result.fail(actuatorNameInUseException());
      }
    }

    // Update the actuator name
    await this.userRepository.updateActuatorName(
      updateActuatorDto.userId,
      updateActuatorDto.actuatorId,
      updateActuatorDto.name
    );

    return Result.success<UpdateActuatorNameResponseDto>({
      success: true,
      message: 'Actuator name updated successfully'
    });
  }

  get name(): string {
    return this.constructor.name;
  }
}
