import { IApplicationService, Result } from '@core/core';

import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { DeleteSensorTriggersEntryDto } from '../dto/entry/delete-sensor-triggers-entry.application.dto'

export class DeleteSensorTriggersApplicationService
  implements IApplicationService<DeleteSensorTriggersEntryDto, string>
{
  private readonly triggerRepository: ITriggerRepository;

  constructor(
    triggerRepository: ITriggerRepository
  ) {
    this.triggerRepository = triggerRepository;
  }

  async execute(deleteTriggerEntry: DeleteSensorTriggersEntryDto): Promise<Result<string>> {
    await this.triggerRepository.deleteSensorTriggers(deleteTriggerEntry.sensorId, deleteTriggerEntry.userId);
    return Result.success<string>('triggers eliminados con éxito');
  }

  get name(): string {
    return this.constructor.name;
  }
}
