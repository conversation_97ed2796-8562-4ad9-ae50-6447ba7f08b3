import { ComparisonTypesEnum } from "@modules/modules/trigger/domain/enum/comparison-types.enum"
import { IActuatorAction } from "@modules/modules/trigger/domain/types/actuator-action.type"


export class FindUserTriggersResponseDto {
    id: string;
    name: string;
    creationDate: Date;
    objectiveMeasure?: number[];
    sensorId?: string;
    userId: string;
    comparison: ComparisonTypesEnum;
    actuators: IActuatorAction[];
    objectiveHour?: string;
}