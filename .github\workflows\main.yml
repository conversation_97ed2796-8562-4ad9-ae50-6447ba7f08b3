name: Deploy

on: 
  push:
    branches:
      - master

jobs:
  run-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23.2.0'
      - run: npm ci
      - run: npm run test
  # build-container:
  #   needs: run-tests
  #   name: Build Docker Image
  #   runs-on: ubuntu-latest
  #   environment: production
  #   steps:
  #     - name: Checkout code 
  #       uses: actions/checkout@v3
  #     - name: Login to Docker Hub
  #       uses: docker/login-action@v1
  #       with:
  #         username: ${{ secrets.DOCKERHUB_USERNAME }}
  #         password: ${{ secrets.DOCKERHUB_TOKEN }}
  #     - name: Build Docker Image  
  #       uses: docker/build-push-action@v2
  #       with:
  #         context: .
  #         push: true
  #         tags: samuelalonso939/lambda_back:latest, samuelalonso939/lambda_back:${{ github.run_number }}
