import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ICalculation } from '../../../domain/types/calculation.type'

@Schema({ _id: false, collection: 'custom_calculations' })
export class OdmCustomCalculationEntity {
  @Prop({ required: true, type: String })
  _id: string;

  @Prop({ required: true, type: String })
  sensorId: string;

  @Prop({ required: true, type: [{ operation: String, value: Number }] })
  calculations: ICalculation[];
}

export const CustomCalculationSchema = SchemaFactory.createForClass(OdmCustomCalculationEntity);
