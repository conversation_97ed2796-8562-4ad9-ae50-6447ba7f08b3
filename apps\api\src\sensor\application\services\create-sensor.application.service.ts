import { IApplicationService, Result, IdGenerator } from '@core/core';
import { CreateSensorEntryDto } from '../dto/entry/create-sensor.application.dto';
import { CreateSensorResponseDto } from '../dto/response/create-sensor-response.application.dto';
import { Sensor } from '@modules/modules/sensor/domain/sensor';
import { SensorCreatedEvent } from '../events/sensor-created.event';
import { IEventEmitter } from '@core/core/application/event-emitter/event-emitter.interface';

export class CreateSensorApplicationService
  implements IApplicationService<CreateSensorEntryDto, CreateSensorResponseDto>
{
  private readonly uuidGenerator: IdGenerator<string>;
  private readonly eventEmitter: IEventEmitter;

  constructor(uuidGenerator: IdGenerator<string>, eventEmitter: IEventEmitter) {
    this.uuidGenerator = uuidGenerator;
    this.eventEmitter = eventEmitter;
  }

  async execute(createSensorDto: CreateSensorEntryDto): Promise<Result<CreateSensorResponseDto>> {
    const idSensor = await this.uuidGenerator.generateId();

    const createSensor = Sensor.create(idSensor, createSensorDto.name, createSensorDto.type);
    const createdSensorEvent: SensorCreatedEvent = {
      userId: createSensorDto.userId,
      sensor: createSensor,
    };
    const results = await this.eventEmitter.emitAsync('sensor.created', createdSensorEvent);
    for (const result of results) {
      if (!result.isSuccess()) {
        return Result.fail<CreateSensorResponseDto>(result.Error);
      }
    }
    return Result.success<CreateSensorResponseDto>({ sensorCreatedEvent: createdSensorEvent });
  }

  get name(): string {
    return this.constructor.name;
  }
}
