import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Result } from '../../../domain';
import { JwtPayload } from './dto/jwt-payload.interface';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { OdmUserEntity } from '@modules/modules/user/infrastructure/entities/odm-entities/odm-user.entity';
import { OdmUserMapper } from '@modules/modules/user/infrastructure/mappers/odm-mappers/odm-user-mapper';
import { User } from '@modules/modules/user/domain/user';

@Injectable()
export class JwtAuthGuard implements CanActivate {

  constructor(
    private jwtService: JwtService,
    private readonly userRepository: OdmUserRepository,
  ) {
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    if (!request.headers['authorization']) throw new UnauthorizedException();
    const [type, token] = request.headers['authorization'].split(' ') ?? [];
    if (type != 'Bearer' || !token) throw new UnauthorizedException();
    try {
      const payload = await this.jwtService.verifyAsync(token, { secret: process.env.JWT_SECRET_KEY });
      const userData = await this.validate(payload);
      // Payload incluye iat y exp, fecha de firma y de vencimiento
      request['user'] = userData;
    } catch {
      throw new UnauthorizedException();
    }
    return true;
  }

  private async validate(payload: JwtPayload) {
    const user = await this.userRepository.findUserById(payload.id);
    if (!user.isPresent()) throw new Error('Error buscando al usuario a traves del token');
    return user.get();
  }
}
