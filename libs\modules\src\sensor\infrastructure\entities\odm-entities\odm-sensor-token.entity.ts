import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ collection: 'sensor_tokens' })
export class OdmSensorTokenEntity {
  readonly _id: string;

  @Prop({ required: true, type: String, index: true })
  token: string;

  @Prop({ type: Date, default: Date.now })
  creationDate: Date;

  @Prop({ required: true, type: String })
  sensorId: string;

  @Prop({ required: true, type: String })
  userId: string;
}

export const SensorTokenSchema = SchemaFactory.createForClass(OdmSensorTokenEntity);
