import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface';
import { Injectable } from '@nestjs/common';
import { OdmSensorMapper } from '../../mappers/odm-mappers/odm-sensor-mapper';
import { OdmUserEntity } from '@modules/modules/user/infrastructure/entities/odm-entities/odm-user.entity';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Sensor } from '@modules/modules/sensor/domain/sensor';
import { Optional } from '@core/core/domain/optional/optional';
import { OdmCustomCalculationEntity } from '../../entities/odm-entities/odm-custom-calculation.entity'
import { CustomCalculation } from '@modules/modules/sensor/domain/custom-calculation'
import { OdmCustomCalculationMapper } from '../../mappers/odm-mappers/odm-custom-calculation-mapper'

@Injectable()
export class OdmSensorRepository implements ISensorRepository {
  constructor(
    @InjectModel(OdmUserEntity.name)
    private readonly userModel: Model<OdmUserEntity>,
    @InjectModel(OdmCustomCalculationEntity.name)
    private readonly customCalculationModel: Model<OdmCustomCalculationEntity>,
    private readonly sensorMapper: OdmSensorMapper,
    private readonly customCalculationMapper: OdmCustomCalculationMapper,
  ) {}

  async findSensor(userId: string, sensorId: string): Promise<Optional<Sensor>> {
    const user = await this.userModel.findById(userId).populate('sensors').exec();
    if (!user) {
      return Optional.of<Sensor>(null);
    }
    const sensor = user.sensors.find((sensor) => sensor._id.toString() === sensorId);
    if (!sensor) {
      return Optional.of<Sensor>(null);
    }
    return Optional.of(await this.sensorMapper.fromPersistenceToDomain(sensor));
  }

  async findSensorCustomCalculation(sensorId: string): Promise<Optional<CustomCalculation>> {
    const customCalculation = await this.customCalculationModel.findOne({ sensorId: sensorId });
    if (!customCalculation) {
      return Optional.of<CustomCalculation>(null);
    }
    return Optional.of(await this.customCalculationMapper.fromPersistenceToDomain(customCalculation));
  }

  async saveCustomCalculation(customCalculation: CustomCalculation): Promise<void> {
    const customCalculationOdm = await this.customCalculationMapper.fromDomainToPersistence(customCalculation);
    const newCustomCalculationOdm = new this.customCalculationModel(customCalculationOdm);
    await newCustomCalculationOdm.save();
  }
}
