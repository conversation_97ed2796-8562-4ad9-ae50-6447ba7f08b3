import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { NestEventEmitter } from './infrastructure/event-emitter/nest-event-emitter';
import { MqttService } from './infrastructure/mqtt/services/mqtt.service'

@Global()
@Module({
  imports: [ConfigModule.forRoot({ isGlobal: true })],
  providers: [NestEventEmitter, MqttService],
  exports: [NestEventEmitter, MqttService],
})
export class CoreModule {}
