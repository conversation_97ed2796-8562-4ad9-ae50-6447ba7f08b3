import { OdmActuatorEntity } from '@modules/modules/actuator/infrastructure/entities/odm-entities/odm-actuator.entity'
import { OdmSensorEntity } from '@modules/modules/sensor/infrastructure/entities/odm-entities/odm-sensor.entity'
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ _id: false, collection: 'users' })
export class OdmUserEntity {
  @Prop({ required: true, type: String })
  _id: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ type: [OdmSensorEntity], default: [] })
  sensors: OdmSensorEntity[];

  @Prop({ type: [OdmActuatorEntity], default: [] })
  actuators: OdmActuatorEntity[];
}

export const UserSchema = SchemaFactory.createForClass(OdmUserEntity);
