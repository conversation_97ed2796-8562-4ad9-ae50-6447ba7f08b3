import { SensorTypesEnum } from '@modules/modules/sensor/domain/enums/sensor-types.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';

export class CreateSensorEntryInfraestructureDto {
  @ApiProperty({
    required: false,
    example: 'Sensor 1',
  })
  @IsString()
  name: string;

  @ApiProperty({
    required: false,
    example: 'tds',
  })
  @IsEnum(SensorTypesEnum)
  type: SensorTypesEnum;
}
