import { Inject, Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketGateway,
} from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { MeasurementDto } from '../dto/measurement.dto';
import { OdmMeasurementRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-measurement-repository';
import { OdmSensorRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-sensor-repository';
import { CreateMeasurementApplicationService } from '../../application/services/create-measurement.application.service';
import {
  ExceptionDecorator,
  HttpExceptionHandler,
  LoggingDecorator,
  NativeLogger,
  PerformanceDecorator,
  UuidGenerator,
} from '@core/core';
import { OdmSensorTokenRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-sensor-token-repository';
import { SensorConnectionService } from '../services/sensor-connection.service';
import { NestEventEmitter } from '@core/core/infrastructure/event-emitter/nest-event-emitter';
import { UserConnectionService } from '../services/user-connection.service';

@WebSocketGateway(4001, { cors: true })
export class SensorGateway implements OnGatewayConnection, OnGatewayDisconnect {
  constructor(
    @Inject() private readonly eventEmitter: NestEventEmitter,
    private readonly measurementRepository: OdmMeasurementRepository,
    private readonly sensorRepository: OdmSensorRepository,
    private readonly sensorTokenRepository: OdmSensorTokenRepository,
    private readonly sensorConnectionService: SensorConnectionService,
    private readonly userConnectionService: UserConnectionService,
  ) {}

  private readonly logger = new Logger('SensorGateway');

  async handleConnection(client: Socket) {
    this.logger.log(`Client ${client.id} connected`);

    const token = client.handshake.headers.authorization as string;

    try {
      const result = await this.sensorTokenRepository.validateToken(token);

      if (!result.isPresent()) {
        this.logger.error(`Invalid token: ${token}`);
        client.disconnect();
        return;
      }

      const sensorInfo = result.get();

      await this.sensorConnectionService.register(client, sensorInfo.userId, sensorInfo.sensorId);

      this.userConnectionService.notifySensorConnection(sensorInfo.userId, sensorInfo.sensorId);
    } catch {
      this.logger.error(`No auth sent by client: ${client.id}`);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const sensor = this.sensorConnectionService.getConnectedSensor(client.id);
    const { userId, sensorId } = sensor.get();
    this.userConnectionService.notifySensorDisconnection(userId, sensorId);
    this.sensorConnectionService.removeSensor(client.id);
    this.logger.warn(`Client ${client.id} disconnected`);
  }

  @SubscribeMessage('measurement')
  handleMeasurement(@ConnectedSocket() client: Socket, @MessageBody() data: MeasurementDto) {
    if (data.measure === undefined) {
      this.logger.error(`Client ${client.id} sent invalid measurement`);
      return;
    }

    const result = this.sensorConnectionService.getConnectedSensor(client.id);

    if (!result.isPresent()) {
      this.logger.error(`Client ${client.id} is not connected`);
      return;
    }

    const sensorConnection = result.get();

    new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new CreateMeasurementApplicationService(
            new UuidGenerator(),
            this.measurementRepository,
            this.sensorRepository,
            this.eventEmitter,
          ),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      sensorId: sensorConnection.sensorId,
      userId: sensorConnection.userId,
      measure: data.measure,
    });

    this.userConnectionService.notifyMeasurement(sensorConnection.userId, sensorConnection.sensorId, data.measure);
  }
}
