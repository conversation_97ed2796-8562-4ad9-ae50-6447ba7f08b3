import { Inject, Logger } from '@nestjs/common';
import { WebSocketGateway } from '@nestjs/websockets';
import { MeasurementDto } from '../dto/measurement.dto';
import { OdmMeasurementRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-measurement-repository';
import { OdmSensorRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-sensor-repository';
import { CreateMeasurementApplicationService } from '../../application/services/create-measurement.application.service';
import {
  ExceptionDecorator,
  HttpExceptionHandler,
  IPushSender,
  LoggingDecorator,
  NativeLogger,
  PerformanceDecorator,
  UuidGenerator,
} from '@core/core';
import { OdmSensorTokenRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-sensor-token-repository';
import { NestEventEmitter } from '@core/core/infrastructure/event-emitter/nest-event-emitter';
import { UserConnectionService } from '../services/user-connection.service';
import { FindSensorTriggersApplicationService } from '../../../trigger/application/services/find-sensor-triggers.service';
import { OdmTriggerRepository } from '@modules/modules/trigger/infrastructure/repositories/odm-repositories/odm-trigger-repository';
import { IActuatorAction } from '@modules/modules/trigger/domain/types/actuator-action.type';
import { ExecuteActionActuatorService } from '../../../../../api/src/actuators/application/services/execute-action-actuator.service';
import { MqttService } from '@core/core/infrastructure/mqtt/services/mqtt.service';
import { OdmActuatorRepository } from '@modules/modules/actuator/infrastructure/repositories/odm-repositories/odm-actuator-repository';
import { ComparisonTypesEnum } from '@modules/modules/trigger/domain/enum/comparison-types.enum';
import { SendPushUserInfraService } from '@modules/modules/common/infrastructure/services/send-push-user.service';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { FirebaseNotifier } from '@core/core/infrastructure/notifier/firebase-notifier-singleton';
import { MqttTopics } from '@core/core/application/messages-sender/enum/mqtt-topics.enum'

@WebSocketGateway({
  path: '/sensor-ws',
  cors: {
    origin: [`${process.env.FRONTEND_URL}`],
    methods: ['GET', 'POST']
  },
  transports: ['polling', 'websocket'],
  allowUpgrades: true,
  namespace: '/sensor',
})
export class SensorGateway {
  private  notifier: IPushSender;
  constructor(
    @Inject() private readonly eventEmitter: NestEventEmitter,
    private readonly measurementRepository: OdmMeasurementRepository,
    private readonly sensorRepository: OdmSensorRepository,
    private readonly triggerRepository: OdmTriggerRepository,
    private readonly sensorTokenRepository: OdmSensorTokenRepository,
    private readonly userConnectionService: UserConnectionService,
    private readonly userRepository: OdmUserRepository,
    @Inject() private readonly mqttService: MqttService,
    private readonly actuatorRepository: OdmActuatorRepository,
  ) {
    this.notifier = FirebaseNotifier.getInstance();
    this.mqttService.subscribe(`${MqttTopics.SENSOR_MEASURE}/#`, (message, receivedTopic) => {
      const data = JSON.parse(message);
      this.handleMeasurement({
        measure: data[0].measure,
        token: data[0].token,
      });
    });
  }

  private readonly logger = new Logger('SensorGateway');

  async handleMeasurement(data: MeasurementDto) {
    if (data.measure === undefined) {
      this.logger.error(`Client ${data.token} sent invalid measurement`);
      return;
    }

    const tokenValidation = await this.sensorTokenRepository.validateToken(data.token);

    if (!tokenValidation.isPresent()) {
      this.logger.error(`Client ${data.token} sent invalid token`);
      return;
    }
    const sensorId = tokenValidation.get().sensorId;
    const userId = tokenValidation.get().userId;
    new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new CreateMeasurementApplicationService(
            new UuidGenerator(),
            this.measurementRepository,
            this.sensorRepository,
            this.eventEmitter,
          ),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      sensorId: sensorId,
      userId: userId,
      measure: data.measure,
    });

    this.userConnectionService.notifyMeasurement(userId, sensorId, data.measure);
    this.checkSensorTriggers(sensorId, data.measure, userId);
  }

  private async checkSensorTriggers(sensorId: string, measure: number, userId: string) {
    const result = await new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindSensorTriggersApplicationService(this.triggerRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      userId: userId,
      sensorId: sensorId,
    });
    for (const trigger of result.Value) {
      switch (trigger.comparison) {
        case ComparisonTypesEnum.eq:
          if (measure == trigger.objectiveMeasure![0]) {
            if ((await this.executeTrigger(trigger.actuators, userId)) == false)
              await this.sendNotification(
                userId,
                'Trigger fallido',
                'El trigger ' + trigger.name + ' ha fallado, por favor revise sus actuadores',
              );
            else
              await this.sendNotification(
                userId,
                'Trigger ejecutado!',
                'Su trigger ' + trigger.name + ' ha sido ejecutado!',
              );
          }
          break;
        case ComparisonTypesEnum.g:
          if (measure > trigger.objectiveMeasure![0]) {
            if ((await this.executeTrigger(trigger.actuators, userId)) == false)
              await this.sendNotification(
                userId,
                'Trigger fallido',
                'El trigger ' + trigger.name + ' ha fallado, por favor revise sus actuadores',
              );
            else
              await this.sendNotification(
                userId,
                'Trigger ejecutado!',
                'Su trigger ' + trigger.name + ' ha sido ejecutado!',
              );
          }
          break;
        case ComparisonTypesEnum.ge:
          if (measure >= trigger.objectiveMeasure![0]) {
            if ((await this.executeTrigger(trigger.actuators, userId)) == false)
              await this.sendNotification(
                userId,
                'Trigger fallido',
                'El trigger ' + trigger.name + ' ha fallado, por favor revise sus actuadores',
              );
            else
              await this.sendNotification(
                userId,
                'Trigger ejecutado!',
                'Su trigger ' + trigger.name + ' ha sido ejecutado!',
              );
          }
          break;
        case ComparisonTypesEnum.l:
          if (measure < trigger.objectiveMeasure![0]) {
            if ((await this.executeTrigger(trigger.actuators, userId)) == false)
              await this.sendNotification(
                userId,
                'Trigger fallido',
                'El trigger ' + trigger.name + ' ha fallado, por favor revise sus actuadores',
              );
            else
              await this.sendNotification(
                userId,
                'Trigger ejecutado!',
                'Su trigger ' + trigger.name + ' ha sido ejecutado!',
              );
          }
          break;
        case ComparisonTypesEnum.le:
          if (measure <= trigger.objectiveMeasure![0]) {
            if ((await this.executeTrigger(trigger.actuators, userId)) == false)
              await this.sendNotification(
                userId,
                'Trigger fallido',
                'El trigger ' + trigger.name + ' ha fallado, por favor revise sus actuadores',
              );
            else
              await this.sendNotification(
                userId,
                'Trigger ejecutado!',
                'Su trigger ' + trigger.name + ' ha sido ejecutado!',
              );
          }
          break;
        case ComparisonTypesEnum.ne:
          if (measure != trigger.objectiveMeasure![0]) {
            if ((await this.executeTrigger(trigger.actuators, userId)) == false)
              await this.sendNotification(
                userId,
                'Trigger fallido',
                'El trigger ' + trigger.name + ' ha fallado, por favor revise sus actuadores',
              );
            else
              await this.sendNotification(
                userId,
                'Trigger ejecutado!',
                'Su trigger ' + trigger.name + ' ha sido ejecutado!',
              );
          }
          break;
        case ComparisonTypesEnum.in:
          if (measure >= trigger.objectiveMeasure![0] && measure <= trigger.objectiveMeasure![1]) {
            if ((await this.executeTrigger(trigger.actuators, userId)) == false)
              await this.sendNotification(
                userId,
                'Trigger fallido',
                'El trigger ' + trigger.name + ' ha fallado, por favor revise sus actuadores',
              );
            else
              await this.sendNotification(
                userId,
                'Trigger ejecutado!',
                'Su trigger ' + trigger.name + ' ha sido ejecutado!',
              );
          }
          break;
        case ComparisonTypesEnum.out:
          if (measure < trigger.objectiveMeasure![0] || measure > trigger.objectiveMeasure![1]) {
            if ((await this.executeTrigger(trigger.actuators, userId)) == false)
              await this.sendNotification(
                userId,
                'Trigger fallido',
                'El trigger ' + trigger.name + ' ha fallado, por favor revise sus actuadores',
              );
            else
              await this.sendNotification(
                userId,
                'Trigger ejecutado!',
                'Su trigger ' + trigger.name + ' ha sido ejecutado!',
              );
          }
          break;
      }
    }
  }

  private async executeTrigger(actuatorsActions: IActuatorAction[], userId: string) {
    const service = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new ExecuteActionActuatorService(this.actuatorRepository, this.mqttService),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    actuatorsActions.forEach(async (action) => {
      const result = await service.execute({
        userId: userId,
        action: action.action,
        actuatorId: action.actuatorId,
      });
      if (!result.isSuccess()) {
        return false;
      }
    });
    return true;
  }

  private async sendNotification(userId: string, title: string, body: string) {
    const service = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new SendPushUserInfraService(this.userRepository, this.notifier),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    await service.execute({
      userId: userId,
      title: title,
      body: body,
      eventType: 'trigger_executed_notification',
      customRedirect: '/trigger',
    });
  }
}
