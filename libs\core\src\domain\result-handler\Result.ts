import { BadRequestException } from '@nestjs/common';
import { Exception } from '../exception/exception';

export class Result<T> {
  private constructor(
    private readonly value?: T,
    private readonly exception?: Exception,
  ) {
    if (value !== undefined && exception !== undefined)
      throw new Error('Value and error not to be definined same time');
    if (value === undefined && exception === undefined)
      throw new Error('Value and error not to be undefinined same time');
  }

  isSuccess(): boolean {
    if (this.value || this.value == 0) return true;
    return false;
  }

  get Value(): T {
    if (this.isSuccess()) return this.value!;
    throw new BadRequestException('The value does not exists');
  }

  get Error(): Exception {
    if (this.exception) return this.exception;
    throw new BadRequestException('The error does not exists');
  }

  static success<T>(value: T): Result<T> {
    return new Result(value, undefined);
  }

  static fail<T>(exception: Exception) {
    return new Result<T>(undefined, exception);
  }
}
