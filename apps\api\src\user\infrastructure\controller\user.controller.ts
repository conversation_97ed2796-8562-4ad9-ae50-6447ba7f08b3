import { LoggingDecorator, NativeLogger, PerformanceDecorator } from '@core/core';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { Controller, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { ApiTags } from '@nestjs/swagger';
import { SensorCreatedEvent } from 'apps/api/src/sensor/application/events/sensor-created.event';
import { AddSensorUserApplicationService } from '../../application/services/add-sensor-user.application.service';
import { ActuatorCreatedEvent } from 'apps/api/src/actuators/application/events/actuator-created.event'
import { AddActuatorUserApplicationService } from '../../application/services/add-actuator-user.application.service'

@ApiTags('User')
@Controller('user')
export class UserController {
  private readonly logger: Logger;

  constructor(private readonly userRepository: OdmUserRepository) {
    this.logger = new Logger('UserController');
  }

  @OnEvent('sensor.created', { suppressErrors: false })
  async handleAddSensorToUser(sensorCreatedEvent: SensorCreatedEvent) {
    const addSensorToUserService = new LoggingDecorator(
      new PerformanceDecorator(new AddSensorUserApplicationService(this.userRepository), new NativeLogger(this.logger)),
      new NativeLogger(this.logger),
    );
    return await addSensorToUserService.execute({ ...sensorCreatedEvent });
  }

  @OnEvent('actuator.created', { suppressErrors: false })
  async handleAddActuatorToUser(actuatorCreatedEvent: ActuatorCreatedEvent) {
    const addActuatorToUserService = new LoggingDecorator(
      new PerformanceDecorator(new AddActuatorUserApplicationService(this.userRepository), new NativeLogger(this.logger)),
      new NativeLogger(this.logger),
    );
    return await addActuatorToUserService.execute({ ...actuatorCreatedEvent });
  }
}
