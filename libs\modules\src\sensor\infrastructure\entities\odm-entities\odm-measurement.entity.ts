import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ _id: false, collection: 'measurements' })
export class OdmMeasurementEntity {
  @Prop({ required: true, type: String })
  _id: string;

  @Prop({ type: Date, default: Date.now })
  creationDate: Date;

  @Prop({ required: true, type: Number })
  measure: number;

  @Prop({ required: true, type: String })
  sensorId: string;

  @Prop({ required: true, type: String })
  userId: string;
}

export const MeasurementSchema = SchemaFactory.createForClass(OdmMeasurementEntity);
