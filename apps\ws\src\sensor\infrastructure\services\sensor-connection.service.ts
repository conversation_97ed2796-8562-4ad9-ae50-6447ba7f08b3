import { Optional } from '@core/core/domain/optional/optional';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { Injectable } from '@nestjs/common';
import { Socket } from 'socket.io';

interface SensorConnection {
  socket: Socket;
  userId: string;
  sensorId: string;
}

interface ConnectedSensors {
  [id: string]: SensorConnection;
}

interface SensorToSocketClient {
  [sensorId: string]: { socket: Socket };
}

@Injectable()
export class SensorConnectionService {
  constructor(private readonly userRepository: OdmUserRepository) {}

  private connectedSensors: ConnectedSensors = {};
  private sensorToSocketClient: SensorToSocketClient = {};

  // async register(client: Socket, userId: string, sensorId: string): Promise<void> {
  //   const user = await this.userRepository.findUserById(userId);

  //   if (!user.isPresent()) {
  //     throw new Error('User not found');
  //   }

  //   this.checkSensorConnection(sensorId);

  //   this.connectedSensors[client.id] = {
  //     socket: client,
  //     userId,
  //     sensorId,
  //   };

  //   this.sensorToSocketClient[sensorId] = {
  //     socket: client,
  //   };
  // }

  // removeSensor(clientId: string) {
  //   const sensorConnection = this.connectedSensors[clientId];
  //   delete this.connectedSensors[clientId];
  //   delete this.sensorToSocketClient[sensorConnection.sensorId];
  // }

  // async getUserConnectedSensors(userId: string): Promise<{ sensorId: string; connected: boolean }[]> {
  //   const userResult = await this.userRepository.findUserById(userId);

  //   if (!userResult.isPresent()) {
  //     throw new Error('User not found');
  //   }

  //   const user = userResult.get();

  //   const sensorsStatusList: { sensorId: string; connected: boolean }[] = [];

  //   user.Sensors?.forEach((s) => {
  //     const sensorSocket = this.sensorToSocketClient[s.Id];
  //     const status = {
  //       connected: false,
  //       sensorId: s.Id,
  //     };
  //     if (sensorSocket) {
  //       status.connected = true;
  //     }
  //     sensorsStatusList.push(status);
  //   });

  //   return sensorsStatusList;
  // }

  // getConnectedSensor(clientId: string): Optional<SensorConnection> {
  //   const sensorConnection = this.connectedSensors[clientId];

  //   if (sensorConnection) {
  //     return Optional.of(sensorConnection);
  //   }

  //   return Optional.of<SensorConnection>(null);
  // }

  // private checkSensorConnection(sensorId: string) {
  //   for (const clientId of Object.keys(this.connectedSensors)) {
  //     const connectedSensor = this.connectedSensors[clientId];

  //     if (connectedSensor.sensorId === sensorId) {
  //       connectedSensor.socket.disconnect();
  //       break;
  //     }
  //   }
  // }
}
