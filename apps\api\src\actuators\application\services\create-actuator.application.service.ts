import { IApplicationService, Result, IdGenerator } from '@core/core';
import { CreateActuatorEntryDto } from '../dto/entry/create-actuator.application.dto';
import { CreateActuatorResponseDto } from '../dto/response/create-actuator-response.application.dto';
import { Actuator } from '@modules/modules/actuator/domain/actuator';
import { ActuatorCreatedEvent } from '../events/actuator-created.event';
import { IEventEmitter } from '@core/core/application/event-emitter/event-emitter.interface';

export class CreateActuatorApplicationService
  implements IApplicationService<CreateActuatorEntryDto, CreateActuatorResponseDto>
{
  private readonly uuidGenerator: IdGenerator<string>;
  private readonly eventEmitter: IEventEmitter;

  constructor(uuidGenerator: IdGenerator<string>, eventEmitter: IEventEmitter) {
    this.uuidGenerator = uuidGenerator;
    this.eventEmitter = eventEmitter;
  }

  async execute(createActuatorDto: CreateActuatorEntryDto): Promise<Result<CreateActuatorResponseDto>> {
    const idActuator = await this.uuidGenerator.generateId();

    const createActuator = Actuator.create(idActuator, createActuatorDto.name, createActuatorDto.type);
    const createdActuatorEvent: ActuatorCreatedEvent = {
      userId: createActuatorDto.userId,
      actuator: createActuator,
    };
    const results = await this.eventEmitter.emitAsync('actuator.created', createdActuatorEvent);
    for (const result of results) {
      if (!result.isSuccess()) {
        return Result.fail<CreateActuatorResponseDto>(result.Error);
      }
    }
    return Result.success<CreateActuatorResponseDto>({ actuatorCreatedEvent: createdActuatorEvent });
  }

  get name(): string {
    return this.constructor.name;
  }
}
