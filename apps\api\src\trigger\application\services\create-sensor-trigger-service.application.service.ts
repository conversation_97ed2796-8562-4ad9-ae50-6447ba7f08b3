import { IApplicationService, Result, IdGenerator, EncryptorBcrypt } from '@core/core';

import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { User } from '@modules/modules/user/domain/user';
import { userNotFoundException } from 'apps/api/src/user/application/exceptions/not-found';
import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface';
import { CreateSensorTriggerEntryDto } from '../dto/entry/create-sensor-trigger-entry.application.dto';
import { CreateSensorTriggerResponseDto } from '../dto/response/create-sensor-trigger-response.application.dto';
import { sensorNotFoundException } from 'apps/api/src/sensor/application/exceptions/not-found';
import { Trigger } from '@modules/modules/trigger/domain/trigger';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface'
import { actuatorNotFoundException } from 'apps/api/src/actuators/application/exceptions/not-found'

export class CreateSensorTriggerApplicationService
  implements IApplicationService<CreateSensorTriggerEntryDto, CreateSensorTriggerResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly uuidGenerator: IdGenerator<string>;
  private readonly triggerRepository: ITriggerRepository;
  private readonly sensorRepository: ISensorRepository;
  private readonly actuatorRepository: IActuatorRepository;

  constructor(
    sensorRepository: ISensorRepository,
    triggerRepository: ITriggerRepository,
    userRepository: IUserRepository,
    uuidGenerator: IdGenerator<string>,
    actuatorRepository: IActuatorRepository,
  ) {
    this.userRepository = userRepository;
    this.uuidGenerator = uuidGenerator;
    this.triggerRepository = triggerRepository;
    this.sensorRepository = sensorRepository;
    this.actuatorRepository = actuatorRepository;
  }

  async execute(createTriggerEntry: CreateSensorTriggerEntryDto): Promise<Result<CreateSensorTriggerResponseDto>> {
    const findResult = await this.userRepository.findUserById(createTriggerEntry.userId);

    if (!findResult.isPresent()) return Result.fail(userNotFoundException());

    const findSensorResult = await this.sensorRepository.findSensor(
      createTriggerEntry.userId,
      createTriggerEntry.sensorId,
    );

    if (!findSensorResult.isPresent()) return Result.fail(sensorNotFoundException());

    for (const actuator of createTriggerEntry.actuators) {
      const findActuatorResult = await this.actuatorRepository.findActuatorById(createTriggerEntry.userId, actuator.actuatorId);
      if (!findActuatorResult.isPresent()) return Result.fail(actuatorNotFoundException());
    }
    
    const idTrigger = await this.uuidGenerator.generateId();

    const create = Trigger.create(
      idTrigger,
      createTriggerEntry.name,
      new Date(),
      createTriggerEntry.userId,
      createTriggerEntry.comparison,
      createTriggerEntry.actuators,
      createTriggerEntry.objectiveMeasure,
      createTriggerEntry.sensorId,
      undefined,
    );

    await this.triggerRepository.save(create);
    
    return Result.success({trigger: create});
  }

  get name(): string {
    return this.constructor.name;
  }
}
