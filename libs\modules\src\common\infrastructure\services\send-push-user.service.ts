import { IApplicationService, IEncryptor, Result, IJwtGenerator, IPushSender } from '@core/core';

import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { SendPushUserEntryDto } from '../dto/entry/send-push-user-entry.dto'
import { noTokensException } from '../exceptions/push-error.exception'

export class SendPushUserInfraService implements IApplicationService<SendPushUserEntryDto, string> {
  private readonly userRepository: IUserRepository;
  private readonly notifier: IPushSender;

  constructor(userRepository: IUserRepository, notifier: IPushSender) {
    this.userRepository = userRepository;
    this.notifier = notifier;
  }

  async execute(pushDto: SendPushUserEntryDto): Promise<Result<string>> {
    const tokens = await this.userRepository.getNotificationTokensForUser(pushDto.userId);
    if (tokens.length === 0) return Result.fail<string>(noTokensException());
    for (const token of tokens) {
      await this.notifier.sendNotificationPush({
        token: token,
        customRedirect: pushDto.customRedirect,
        eventType: pushDto.eventType,
        notification: {
          title: pushDto.title,
          body: pushDto.body,
        },
      });
    }
    return Result.success('push_sended');
  }

  get name(): string {
    return this.constructor.name;
  }
}
