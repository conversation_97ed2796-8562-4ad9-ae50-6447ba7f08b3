import { Modu<PERSON> } from '@nestjs/common';
import { OdmUserMapper } from './mappers/odm-mappers/odm-user-mapper';
import { OdmUserRepository } from './repositories/odm-repository/odm-user-repository';
import { MongooseModule } from '@nestjs/mongoose';
import { OdmUserEntity, UserSchema } from './entities/odm-entities/odm-user.entity';
import { TriggerEntityModule } from '@modules/modules/trigger/infrastructure/trigger-entity.module'

@Module({
  imports: [MongooseModule.forFeature([{ name: OdmUserEntity.name, schema: UserSchema }]), TriggerEntityModule],
  providers: [OdmUserMapper, OdmUserRepository],
  exports: [MongooseModule, OdmUserRepository],
})
export class UserEntityModule {}
