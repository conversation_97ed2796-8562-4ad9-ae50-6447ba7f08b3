import { IApplicationService, Result, IdGenerator, EncryptorBcrypt } from '@core/core';

import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { User } from '@modules/modules/user/domain/user';
import { userNotFoundException } from 'apps/api/src/user/application/exceptions/not-found';
import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { sensorNotFoundException } from 'apps/api/src/sensor/application/exceptions/not-found';
import { Trigger } from '@modules/modules/trigger/domain/trigger';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface'
import { actuatorNotFoundException } from 'apps/api/src/actuators/application/exceptions/not-found'
import { DeleteTriggerEntryDto } from '../dto/entry/delete-trigger-entry.application.dto'
import { DeleteTriggerResponseDto } from '../dto/response/delete-trigger-response.application.dto'
import { triggerNotFoundException } from '../exceptions/not-found'

export class DeleteTriggerApplicationService
  implements IApplicationService<DeleteTriggerEntryDto, DeleteTriggerResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly uuidGenerator: IdGenerator<string>;
  private readonly triggerRepository: ITriggerRepository;
  private readonly actuatorRepository: IActuatorRepository;

  constructor(
    triggerRepository: ITriggerRepository,
    userRepository: IUserRepository,
    uuidGenerator: IdGenerator<string>,
    actuatorRepository: IActuatorRepository,
  ) {
    this.userRepository = userRepository;
    this.uuidGenerator = uuidGenerator;
    this.triggerRepository = triggerRepository;
    this.actuatorRepository = actuatorRepository;
  }

  async execute(deleteTriggerEntry: DeleteTriggerEntryDto): Promise<Result<DeleteTriggerResponseDto>> {
    const trigger = await this.triggerRepository.findUserTriggerById(deleteTriggerEntry.triggerId, deleteTriggerEntry.userId);
    if (!trigger.isPresent()) return Result.fail(triggerNotFoundException());
    await this.triggerRepository.deleteTrigger(deleteTriggerEntry.triggerId);
    return Result.success<DeleteTriggerResponseDto>({ response: 'trigger eliminado con éxito', timeTrigger: trigger.get().ObjectiveHour !== undefined });
  }

  get name(): string {
    return this.constructor.name;
  }
}
