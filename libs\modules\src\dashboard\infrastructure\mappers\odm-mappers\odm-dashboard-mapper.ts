import { <PERSON><PERSON>ap<PERSON> } from '@core/core';
import { Model } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { Dashboard } from '@modules/modules/dashboard/domain/dashboard';
import { OdmDashboardEntity } from '../../entities/odm-entities/odm-dashboard.entity';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class OdmDashboardMapper implements IMapper<Dashboard, OdmDashboardEntity> {
  constructor(
    @InjectModel(OdmDashboardEntity.name)
    private readonly dashboardModel = Model<OdmDashboardEntity>,
  ) {}

  fromDomainToPersistence(domain: Dashboard): Promise<OdmDashboardEntity> {
    return new Promise(async (resolve, reject) => {
      const dashboard = new this.dashboardModel({
        _id: domain.Id,
        userId: domain.UserId,
        sensorId: domain.SensorId,
        position: domain.Position,
        type: domain.Type,
      });
      resolve(dashboard);
    });
  }
  async fromPersistenceToDomain(dashboard: OdmDashboardEntity): Promise<Dashboard> {
    return Dashboard.create(dashboard._id, dashboard.sensorId, dashboard.position, dashboard.userId, dashboard.type);
  }
}
