import { Body, Controller, Post, Logger, Inject, Get, Query, Param, ParseUUIDPipe, Delete } from '@nestjs/common';

import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { UseGuards } from '@nestjs/common/decorators/core/use-guards.decorator';

import {
  ExceptionDecorator,
  GetUser,
  HttpExceptionHandler,
  IdGenerator,
  JwtAuthGuard,
  LoggingDecorator,
  NativeLogger,
  PaginationDto,
  PerformanceDecorator,
  UuidGenerator,
} from '@core/core';

import { User } from '@modules/modules/user/domain/user';
import { NestEventEmitter } from '@core/core/infrastructure/event-emitter/nest-event-emitter';
import { OdmDashboardRepository } from '@modules/modules/dashboard/infrastructure/repositories/odm-repositories/odm-dashboard-repository';
import { CreateDashboardSwaggerResponseDto } from './dto/response/create-dashboard-swagger-response.dto';
import { CreateDashboardApplicationService } from '../../application/services/create-dashboard-service.application.service';
import { OdmSensorRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-sensor-repository';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { CreateDashboardEntryInfraDto } from './dto/entry/create-dashboard-entry.dto';
import { FindUserDashboardsApplicationService } from '../../application/services/find-user-dashboards.service';
import { FindManyMeasurementsInfraestructureDto } from 'apps/api/src/sensor/infrastructure/controller/dto/entry/find-many-measurements.dto';
import { OdmMeasurementRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-measurement-repository';
import { FindManyMeasurementsApplicationService } from 'apps/api/src/sensor/application/services/find-many/find-many-measurements.service';
import { FindUserDashboardsSwaggerResponseDto } from './dto/response/find-user-dashboards-swagger-response.dto';
import { DeleteDashboardApplicationService } from '../../application/services/delete-dashboard.service';
import { DashboardTypesEnum } from '@modules/modules/dashboard/domain/enum/dashboard-types.enum';
import { RecalculateDashboardPositionsApplicationService } from '../../application/services/recalculate-dashboard-positions.service';
import { OnEvent } from '@nestjs/event-emitter'
import { DeleteSensorDashboardsApplicationService } from '../../application/services/delete-sensor-dashboards.application.service'

@ApiTags('Dashboard')
@Controller('dashboard')
export class DashboardController {
  private readonly logger: Logger;
  private readonly uuidGenerator: IdGenerator<string>;

  constructor(
    @Inject() private readonly eventEmitter: NestEventEmitter,
    private readonly dashboardRepository: OdmDashboardRepository,
    private readonly sensorRepository: OdmSensorRepository,
    private readonly userRepository: OdmUserRepository,
    private readonly measurementRepository: OdmMeasurementRepository,
  ) {
    this.logger = new Logger('DashboardController');
    this.uuidGenerator = new UuidGenerator();
  }

  @Post('create')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Registrar un dashboard', type: CreateDashboardSwaggerResponseDto })
  async createDashboard(@GetUser() user: User, @Body() createDto: CreateDashboardEntryInfraDto) {
    const createApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new CreateDashboardApplicationService(
            this.sensorRepository,
            this.dashboardRepository,
            this.userRepository,
            this.uuidGenerator,
          ),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );

    const getDashbardsResult = await new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindUserDashboardsApplicationService(this.dashboardRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      userId: user.Id,
      page: 1,
      limit: 0,
    });

    const dashboards = getDashbardsResult.Value;
    const result = await createApplicationService.execute({
      userId: user.Id,
      sensorId: createDto.sensorId,
      position: dashboards.length + 1,
      type: createDto.type,
    });

    return result.Value;
  }

  @Get('many')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Obtener los dashboards de un usuario', type: [FindUserDashboardsSwaggerResponseDto] })
  async findUserDashboards(
    @GetUser() user: User,
    @Query() query: PaginationDto,
    @Query() measureQuery: FindManyMeasurementsInfraestructureDto,
  ) {
    const result = await new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindUserDashboardsApplicationService(this.dashboardRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      userId: user.Id,
      page: query.page,
      limit: query.limit,
    });

    const dashboards = result.Value;

    const measureService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindManyMeasurementsApplicationService(this.sensorRepository, this.measurementRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );

    const response: FindUserDashboardsSwaggerResponseDto[] = [];

    for (const dashboard of dashboards) {
      const measureResult = await measureService.execute({
        userId: user.Id,
        sensorId: dashboard.sensorId,
        startDate: new Date(measureQuery.startDate),
        endDate: new Date(measureQuery.endDate),
      });
      let measurements = measureResult.Value.map((m) => {
        return {
          measurement: m.measurement,
          timestamp: m.timestamp,
        };
      });

      response.push({
        id: dashboard.id,
        sensorId: dashboard.sensorId,
        userId: dashboard.userId,
        position: dashboard.position,
        type: dashboard.type,
        measurements: measurements.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime()),
      });
    }

    return response;
  }

  @Delete('delete/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Registrar un dashboard', type: String })
  async deleteDashboard(@GetUser() user: User, @Param('id') dashboardId: string) {
    const deleteApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new DeleteDashboardApplicationService(this.dashboardRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    const result = await deleteApplicationService.execute({
      userId: user.Id,
      dashboardId: dashboardId,
    });
    const recalculatePositions = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new RecalculateDashboardPositionsApplicationService(this.dashboardRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({ userId: user.Id });
    return result.Value;
  }

  @OnEvent('sensor.deleted', { suppressErrors: false })
  async handleDeleteDashboard(data: { sensorId: string, userId: string }) {
    const deleteApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new DeleteSensorDashboardsApplicationService(this.dashboardRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    const result = await deleteApplicationService.execute({
      userId: data.userId,
      sensorId: data.sensorId,
    });
    return result.Value;
  }
}
