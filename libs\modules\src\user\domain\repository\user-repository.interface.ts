import { Result } from '@core/core';
import { User } from '../user';
import { Sensor } from '@modules/modules/sensor/domain/sensor';
import { Optional } from '@core/core/domain/optional/optional';
import { Actuator } from '@modules/modules/actuator/domain/actuator'

export interface IUserRepository {
  saveUser(user: User): Promise<void>;

  findUserById(id: string): Promise<Optional<User>>;

  findUserByEmail(email: string): Promise<Optional<User>>;

  findAllUser(): Promise<User[]>;

  addSensorToUser(userId: string, sensor: Sensor): Promise<void>;

  userHasSensorByName(userId: string, sensorName: string): Promise<boolean>;

  addActuatorToUser(userId: string, actuator: Actuator): Promise<void>;

  userHasActuatorByName(userId: string, actuatorName: string): Promise<boolean>;

  addNotificationTokenToUser(userId: string, token: string): Promise<void>;

  removeNotificationTokenFromUser(userId: string, token: string): Promise<void>;

  getNotificationTokensForUser(userId: string): Promise<string[]>;
}
