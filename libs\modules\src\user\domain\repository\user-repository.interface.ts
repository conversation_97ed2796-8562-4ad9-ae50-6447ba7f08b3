import { Result } from '@core/core';
import { User } from '../user';
import { Sensor } from '@modules/modules/sensor/domain/sensor';
import { Optional } from '@core/core/domain/optional/optional';
import { Actuator } from '@modules/modules/actuator/domain/actuator'

export interface IUserRepository {
  saveUser(user: User): Promise<void>;

  findUserById(id: string): Promise<Optional<User>>;

  findUserByEmail(email: string): Promise<Optional<User>>;

  findAllUser(): Promise<User[]>;

  addSensorToUser(userId: string, sensor: Sensor): Promise<void>;

  deleteSensorFromUser(userId: string, sensorId: string): Promise<void>;

  updateSensorName(userId: string, sensorId: string, name: string): Promise<void>;

  userHasSensorByName(userId: string, sensorName: string): Promise<boolean>;

  addActuatorToUser(userId: string, actuator: Actuator): Promise<void>;

  deleteActuator<PERSON>romUser(userId: string, actuatorId: string): Promise<void>;

  updateActuatorName(userId: string, actuatorId: string, name: string): Promise<void>;

  userHasActuatorByName(userId: string, actuatorName: string): Promise<boolean>;

  addNotificationTokenToUser(userId: string, token: string): Promise<void>;

  removeNotificationTokenFromUser(userId: string, token: string): Promise<void>;

  getNotificationTokensForUser(userId: string): Promise<string[]>;
}
