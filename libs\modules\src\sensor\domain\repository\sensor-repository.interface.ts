import { Optional } from '@core/core/domain/optional/optional';
import { Sensor } from '../sensor';
import { CustomCalculation } from '../custom-calculation'

export interface ISensorRepository {
  findSensor(userId: string, sensorId: string): Promise<Optional<Sensor>>;
  findSensorCustomCalculation(sensorId: string): Promise<Optional<CustomCalculation>>;
  saveCustomCalculation(customCalculation: CustomCalculation): Promise<void>;
}
