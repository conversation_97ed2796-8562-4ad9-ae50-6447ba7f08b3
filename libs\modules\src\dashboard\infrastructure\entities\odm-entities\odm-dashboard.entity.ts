import { DashboardTypesEnum } from '@modules/modules/dashboard/domain/enum/dashboard-types.enum'
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ _id: false, collection: 'dashboard'})
export class OdmDashboardEntity {
  @Prop({ required: true, type: String })
  _id: string;

  @Prop({ required: true, type: String })
  userId: string;

  @Prop({ required: true, type: String })
  sensorId: string;

  @Prop({ required: true, type: String, enum: DashboardTypesEnum })
  type: DashboardTypesEnum;

  @Prop({ required: true, type: Number })
  position: number;
}

export const DashboardSchema = SchemaFactory.createForClass(OdmDashboardEntity);
