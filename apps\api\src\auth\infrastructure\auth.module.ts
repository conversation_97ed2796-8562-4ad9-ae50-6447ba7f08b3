import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthController } from './controller/auth.controller';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET_KEY,
      signOptions: { expiresIn: '48h' },
    }),
    UserEntityModule,
  ],
  controllers: [AuthController],
  exports: [JwtModule],
})
export class AuthModule {}
