
import { ApiProperty } from '@nestjs/swagger';
import { IsA<PERSON>y, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { ICalculation } from '@modules/modules/sensor/domain/types/calculation.type'

export class CreateSensorCustomCalculationEntryInfrastructureDto {
  @ApiProperty({ description: 'Sensor ID', example: '425169e1-e2ce-43f0-ab60-864500b32da9' })
  @IsString()
  @IsNotEmpty()
  sensorId: string;

  @ApiProperty({ description: 'Calculations', type: 'array' })
  @IsArray()
  calculations: ICalculation[];
}
