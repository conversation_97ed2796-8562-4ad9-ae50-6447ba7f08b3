import { Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketGateway,
} from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { UserConnectionService } from '../services/user-connection.service';
import { JwtPayload } from '@core/core/infrastructure/jwt/decorator/dto/jwt-payload.interface';
import { JwtService } from '@nestjs/jwt';
import { SubscribeDto } from '../dto/subscribe.dto';
import { SensorConnectionService } from '../services/sensor-connection.service';

@WebSocketGateway(4002, { cors: true })
export class MeasurementGateway implements OnGatewayConnection, OnGatewayDisconnect {
  private readonly logger = new Logger('UserGateway');

  constructor(
    private readonly jwtService: JwtService,
    private readonly userConnectionService: UserConnectionService,
    private readonly sensorConnectionService: SensorConnectionService,
  ) {}

  async handleConnection(client: Socket) {
    this.logger.log(`Client ${client.id} connected`);

    const token = client.handshake.headers.authorization as string;
    let payload: JwtPayload;

    try {
      payload = this.jwtService.verify(token);
    } catch {
      this.logger.warn(`Client ${client.id} disconnected due to invalid JWT`);
      client.emit('invalid-jwt');
      client.disconnect();
      return;
    }

    try {
      await this.userConnectionService.register(client, payload.id);
    } catch {
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    this.userConnectionService.removeUser(client.id);
    this.logger.warn(`Client ${client.id} disconnected`);
  }

  @SubscribeMessage('subscribe')
  handleSubscribe(@ConnectedSocket() client: Socket, @MessageBody() data: SubscribeDto) {
    this.logger.log(`Client ${client.id} subscribed to sensor ${data.sensorId}`);
    this.userConnectionService.subscribeSensor(client.id, data.sensorId);
  }

  @SubscribeMessage('unsubscribe')
  handleUnsubscribe(@ConnectedSocket() client: Socket, @MessageBody() data: SubscribeDto) {
    this.logger.log(`Client ${client.id} unsubscribed from sensor ${data.sensorId}`);
    this.userConnectionService.unsubscribeSensor(client.id, data.sensorId);
  }

  // @SubscribeMessage('get-sensors-status')
  // async handleGetSensorsStatuts(@ConnectedSocket() client: Socket) {
  //   const user = this.userConnectionService.getUserFromClientId(client.id).user;
  //   const sensorsStatusList = await this.sensorConnectionService.getUserConnectedSensors(user.Id);

  //   client.emit('sensors-status', sensorsStatusList);
  // }
}
