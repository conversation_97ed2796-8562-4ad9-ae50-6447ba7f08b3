import { IApplicationService, Result, IdGenerator, EncryptorBcrypt } from '@core/core';

import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { User } from '@modules/modules/user/domain/user';
import { userNotFoundException } from 'apps/api/src/user/application/exceptions/not-found';
import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { sensorNotFoundException } from 'apps/api/src/sensor/application/exceptions/not-found';
import { Trigger } from '@modules/modules/trigger/domain/trigger';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface'
import { CreateTimeTriggerResponseDto } from '../dto/response/create-time-trigger-response.application.dto'
import { CreateTimeTriggerEntryDto } from '../dto/entry/create-time-trigger-entry.application.dto'
import { actuatorNotFoundException } from 'apps/api/src/actuators/application/exceptions/not-found'

export class CreateTimeTriggerApplicationService
  implements IApplicationService<CreateTimeTriggerEntryDto, CreateTimeTriggerResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly uuidGenerator: IdGenerator<string>;
  private readonly triggerRepository: ITriggerRepository;
  private readonly actuatorRepository: IActuatorRepository;

  constructor(
    triggerRepository: ITriggerRepository,
    userRepository: IUserRepository,
    uuidGenerator: IdGenerator<string>,
    actuatorRepository: IActuatorRepository,
  ) {
    this.userRepository = userRepository;
    this.uuidGenerator = uuidGenerator;
    this.triggerRepository = triggerRepository;
    this.actuatorRepository = actuatorRepository;
  }

  async execute(createTriggerEntry: CreateTimeTriggerEntryDto): Promise<Result<CreateTimeTriggerResponseDto>> {
    const findResult = await this.userRepository.findUserById(createTriggerEntry.userId);

    if (!findResult.isPresent()) return Result.fail(userNotFoundException());

    for (const actuator of createTriggerEntry.actuators) {
      const findActuatorResult = await this.actuatorRepository.findActuatorById(createTriggerEntry.userId, actuator.actuatorId);
      if (!findActuatorResult.isPresent()) return Result.fail(actuatorNotFoundException());
    }
    
    const idTrigger = await this.uuidGenerator.generateId();

    const objectiveHour = `${createTriggerEntry.objectiveMinute} ${createTriggerEntry.objectiveHour} * * ${createTriggerEntry.objectiveDays.join(',')}`;

    const create = Trigger.create(
      idTrigger,
      createTriggerEntry.name,
      new Date(),
      createTriggerEntry.userId,
      createTriggerEntry.comparison,
      createTriggerEntry.actuators,
      undefined,
      undefined,
      objectiveHour,
    );

    await this.triggerRepository.save(create);
    
    return Result.success({trigger: create});
  }

  get name(): string {
    return this.constructor.name;
  }
}
