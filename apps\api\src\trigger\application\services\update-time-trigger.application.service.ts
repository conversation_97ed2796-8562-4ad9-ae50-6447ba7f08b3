import { IApplicationService, Result, IdGenerator } from '@core/core';
import { UpdateTimeTriggerEntryDto } from '../dto/entry/update-time-trigger-entry.application.dto';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { userNotFoundException } from 'apps/api/src/user/application/exceptions/not-found';
import { triggerNotFoundException } from '../exceptions/not-found';
import { invalidTriggerTypeException } from '../exceptions/invalid-trigger-type';
import { Trigger } from '@modules/modules/trigger/domain/trigger';
import { UpdateTimeTriggerResponseDto } from '../dto/response/update-time-trigger-response.application';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface';
import { actuatorNotFoundException } from 'apps/api/src/actuators/application/exceptions/not-found';

export class UpdateTimeTriggerApplicationService
  implements IApplicationService<UpdateTimeTriggerEntryDto, UpdateTimeTriggerResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly triggerRepository: ITriggerRepository;
  private readonly actuatorRepository: IActuatorRepository;

  constructor(
    triggerRepository: ITriggerRepository,
    userRepository: IUserRepository,
    actuatorRepository: IActuatorRepository,
  ) {
    this.userRepository = userRepository;
    this.triggerRepository = triggerRepository;
    this.actuatorRepository = actuatorRepository;
  }

  async execute(updateTriggerEntry: UpdateTimeTriggerEntryDto): Promise<Result<UpdateTimeTriggerResponseDto>> {
    // Find the trigger
    const triggerResult = await this.triggerRepository.findUserTriggerById(
      updateTriggerEntry.triggerId,
      updateTriggerEntry.userId,
    );
    if (!triggerResult.isPresent()) {
      return Result.fail(triggerNotFoundException());
    }

    const existingTrigger = triggerResult.get();

    // Validate all actuators exist
    for (const actuator of updateTriggerEntry.actuators) {
      const actuatorResult = await this.actuatorRepository.findActuatorById(
        updateTriggerEntry.userId,
        actuator.actuatorId,
      );
      if (!actuatorResult.isPresent()) {
        return Result.fail(actuatorNotFoundException());
      }
    }
    // Validate that this is a time trigger (has objectiveHour and no sensorId)
    if (!existingTrigger.ObjectiveHour || existingTrigger.SensorId) {
      return Result.fail(invalidTriggerTypeException());
    }

    // Create the new cron string
    const objectiveHour = `${updateTriggerEntry.objectiveMinute} ${updateTriggerEntry.objectiveHour} * * ${updateTriggerEntry.objectiveDays.join(',')}`;

    // Create updated trigger with new time data
    existingTrigger.update(
      updateTriggerEntry.name,
      existingTrigger.CreationDate,
      existingTrigger.UserId,
      existingTrigger.Comparison,
      updateTriggerEntry.actuators,
      undefined,
      undefined,
      objectiveHour,
    );

    // Update the trigger
    await this.triggerRepository.updateTrigger(existingTrigger);

    return Result.success<UpdateTimeTriggerResponseDto>({
      response: 'Time trigger updated successfully',
    });
  }

  get name(): string {
    return this.constructor.name;
  }
}
