import { ApiProperty } from '@nestjs/swagger';

export class EmqxClientDto {
  @ApiProperty({ description: 'Client ID' })
  clientid: string;

  @ApiProperty({ description: 'Username' })
  username: string;

  @ApiProperty({ description: 'Connection status' })
  connected: boolean;

  @ApiProperty({ description: 'Connection timestamp' })
  connected_at: string;

  @ApiProperty({ description: 'Disconnection timestamp', required: false })
  disconnected_at?: string;

  @ApiProperty({ description: 'IP address' })
  ip_address: string;

  @ApiProperty({ description: 'Port number' })
  port: number;

  @ApiProperty({ description: 'Protocol used' })
  protocol: string;
}

export class EmqxClientsMetaDto {
  @ApiProperty({ description: 'Total count of clients' })
  count: number;

  @ApiProperty({ description: 'Limit per page' })
  limit: number;

  @ApiProperty({ description: 'Current page' })
  page: number;
}

export class EmqxClientsResponseDto {
  @ApiProperty({ type: [EmqxClientDto], description: 'Array of EMQX clients' })
  data: EmqxClientDto[];

  @ApiProperty({ type: EmqxClientsMetaDto, description: 'Metadata for pagination' })
  meta: EmqxClientsMetaDto;
}
