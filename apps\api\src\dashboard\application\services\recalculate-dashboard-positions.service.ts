import { ApplicationServiceEntryDto, IApplicationService, Result } from '@core/core';
import { IDashboardRepository } from '@modules/modules/dashboard/domain/repository/dashboard-repository.interface';
import { RecalculateDashboardPositionsResponseDto } from '../dto/response/recalculate-dashboard-position-response.application.dto';

export class RecalculateDashboardPositionsApplicationService
  implements IApplicationService<ApplicationServiceEntryDto, RecalculateDashboardPositionsResponseDto>
{
  constructor(private readonly dashboardsRepository: IDashboardRepository) {}

  async execute(data: ApplicationServiceEntryDto): Promise<Result<RecalculateDashboardPositionsResponseDto>> {
    const dashboards = await this.dashboardsRepository.findDashboardsByUserId(data.userId, 1, 0);
    let position = 1
    for (const dasboard of dashboards){
      dasboard.changePosition(position)
      await this.dashboardsRepository.updateDashboardPosition(dasboard.Id, dasboard.Position)
      position ++;
    }

    return Result.success({ response: 'dashboards recalculado con éxito' });
  }
  get name(): string {
    return this.constructor.name;
  }
}
