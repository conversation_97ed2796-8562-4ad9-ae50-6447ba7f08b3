import { ApiProperty } from '@nestjs/swagger';

export class SignUpUserSwaggerResponseDto {
  @ApiProperty({ example: { email: 'calon<PERSON>@gmail.com', name: '<PERSON>', phone: '04121231231' } })
  user: {
    email: string;
    name: string;
  };
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiJ9.aHVhbG9uZy5jaGlhbmdAZ21bacwuY29t.PhujWyxfi7WRJyPPryrf2IlPtkpEyQ6BXnFgXXWw0N8',
  })
  token: string;
}
