import { Measurement } from '@modules/modules/sensor/domain/measurement';
import { IMeasurementRepository } from '@modules/modules/sensor/domain/repository/measurement-repository.interface';
import { Model } from 'mongoose';
import { OdmMeasurementMapper } from '../../mappers/odm-mappers/odm-measurement-mapper';
import { OdmMeasurementEntity } from '../../entities/odm-entities/odm-measurement.entity';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Optional } from '@core/core/domain/optional/optional';
/* import { PaginationInfo } from '@core/core/infrastructure/pagination/interfaces/pagination.info';
import { makePaginationInfo } from '@core/core/infrastructure/pagination/helpers/pagination.factory'; */

@Injectable()
export class OdmMeasurementRepository implements IMeasurementRepository {
  constructor(
    @InjectModel(OdmMeasurementEntity.name)
    private readonly measurementModel: Model<OdmMeasurementEntity>,
    private readonly odmMapper: OdmMeasurementMapper,
  ) {
    this.measurementModel = measurementModel;
    this.odmMapper = odmMapper;
  }
  async save(measurement: Measurement): Promise<void> {
    const measurementOdm = await this.odmMapper.fromDomainToPersistence(measurement);
    const newMeasurementOdm = new this.measurementModel(measurementOdm);
    await newMeasurementOdm.save();
  }

  async findMany(data: { sensorId: string; startDate: Date; endDate: Date }): Promise<Measurement[]> {
    const { startDate, endDate, sensorId } = data;

    const measurements = await this.measurementModel
      .find({ sensorId: sensorId, creationDate: { $gte: startDate, $lte: endDate } })
      .sort({ creationDate: -1 });

    return await Promise.all(measurements.map((measurement) => this.odmMapper.fromPersistenceToDomain(measurement)));
  }

  /* async findManyPagination(data: { sensorId: string; startDate: Date; endDate: Date }): Promise<PaginationInfo> {
    const { limit, page, sensorId } = data;
    const count = await this.measurementModel.countDocuments({ sensorId: sensorId });
    return makePaginationInfo({ count, page, limit });
  } */

  async getLastMeasurement(sensorId: string): Promise<Optional<Measurement>> {
    const measurement = await this.measurementModel.findOne({ 'sensor._id': sensorId }).sort({ creationDate: -1 });
    if (measurement) {
      return Optional.of(await this.odmMapper.fromPersistenceToDomain(measurement));
    }
    return Optional.of<Measurement>(null);
  }
  async getAllMeasurements(sensorId: string): Promise<Measurement[]> {
    const measurements = await this.measurementModel.find({ 'sensor._id': sensorId });

    return await Promise.all(measurements.map((measurement) => this.odmMapper.fromPersistenceToDomain(measurement)));
  }
}
