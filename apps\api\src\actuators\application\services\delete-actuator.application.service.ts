import { IApplicationService, Result, IdGenerator } from '@core/core';
import { DeleteActuatorEntryDto } from '../dto/entry/delete-actuator.application.dto';
import { DeleteActuatorResponseDto } from '../dto/response/delete-actuator-response.application.dto';
import { IMessageSender } from '@core/core/application/messages-sender/message-sender.interface'
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface'
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface'
import { actuatorNotFoundException } from '../exceptions/not-found'
import { IEventEmitter } from '@core/core/application/event-emitter/event-emitter.interface'

export class DeleteActuatorApplicationService
  implements IApplicationService<DeleteActuatorEntryDto, DeleteActuatorResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly actuatorRepository: IActuatorRepository;
  private readonly messageSender: IMessageSender;
  private readonly eventEmitter: IEventEmitter;

  constructor(userRepository: IUserRepository, messageSender: IMessageSender, actuatorRepository: IActuatorRepository, eventEmitter: IEventEmitter) {
    this.messageSender = messageSender;
    this.userRepository = userRepository;
    this.actuatorRepository = actuatorRepository;
    this.eventEmitter = eventEmitter;
  }

  async execute(deleteActuatorDto: DeleteActuatorEntryDto): Promise<Result<DeleteActuatorResponseDto>> {
    const actuator = await this.actuatorRepository.findActuatorById(deleteActuatorDto.userId, deleteActuatorDto.actuatorId);
    if (!actuator.isPresent()) return Result.fail(actuatorNotFoundException());
    await this.userRepository.deleteActuatorFromUser(deleteActuatorDto.userId, deleteActuatorDto.actuatorId);

    this.messageSender.emit('delete-actuator/' + deleteActuatorDto.actuatorId, JSON.stringify({actuator: deleteActuatorDto.actuatorId}));
    await this.eventEmitter.emitAsync('actuator.deleted', {actuatorId: deleteActuatorDto.actuatorId, userId: deleteActuatorDto.userId });
    return Result.success<DeleteActuatorResponseDto>({ response: 'actuator eliminado con éxito' });
  }

  get name(): string {
    return this.constructor.name;
  }
}
