import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/infrastructure/auth.module';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { CoreModule } from '@core/core/core.module';
import { ActuatorEntityModule } from '@modules/modules/actuator/infrastructure/actuator-entity.module'
import { ActuatorController } from './controller/actuator.controller'

@Module({
  imports: [ActuatorEntityModule, AuthModule, UserEntityModule, CoreModule],
  controllers: [ActuatorController],
})
export class ActuatorModule {}
