import { IApplicationService, Result } from '@core/core';
import { FindOneSensorEntryDto } from './dto/entry';
import { FindOneSensorResponseDto } from './dto/response';
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { userNotFoundException } from 'apps/api/src/user/application/exceptions/not-found';
import { sensorNotFoundException } from '../../exceptions/not-found';

export class FindOneSensorApplicationService
  implements IApplicationService<FindOneSensorEntryDto, FindOneSensorResponseDto>
{
  constructor(
    private readonly userRepository: IUserRepository,
    private readonly sensorRepository: ISensorRepository,
  ) {}

  async execute(data: FindOneSensorEntryDto): Promise<Result<FindOneSensorResponseDto>> {
    const { sensorId, userId } = data;

    const userResult = await this.userRepository.findUserById(userId);
    if (!userResult.isPresent()) {
      return Result.fail(userNotFoundException());
    }

    const sensorResult = await this.sensorRepository.findSensor(userId, sensorId);

    if (!sensorResult.isPresent()) {
      return Result.fail(sensorNotFoundException());
    }

    const sensor = sensorResult.get();

    return Result.success({
      id: sensor.Id,
      name: sensor.Name,
      type: sensor.Type,
    });
  }
  get name(): string {
    return this.constructor.name;
  }
}
