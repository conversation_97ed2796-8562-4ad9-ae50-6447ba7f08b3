import { IEventEmitter } from '@core/core/application/event-emitter/event-emitter.interface';
import { Injectable } from '@nestjs/common';
import { EventEmitter2, EventEmitterReadinessWatcher } from '@nestjs/event-emitter';

@Injectable()
export class NestEventEmitter implements IEventEmitter {
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly eventEmitterRedinessWatcher: EventEmitterReadinessWatcher,
  ) {}

  async emit(event: string, payload: object): Promise<boolean> {
    await this.eventEmitterRedinessWatcher.waitUntilReady();
    return this.eventEmitter.emit(event, payload);
  }

  async emitAsync(event: string, payload: object): Promise<any[]> {
    await this.eventEmitterRedinessWatcher.waitUntilReady();
    return await this.eventEmitter.emitAsync(event, payload);
  }
}
