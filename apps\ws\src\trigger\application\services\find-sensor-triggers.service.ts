import { IApplicationService, Result } from '@core/core';
import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { FindSensorTriggersResponseDto } from '../dto/response/find-sensor-triggers-response.application.dto'
import { FindSensorTriggersEntryDto } from '../dto/entry/find-sensor-triggers.application.dto'

export class FindSensorTriggersApplicationService
  implements IApplicationService<FindSensorTriggersEntryDto, FindSensorTriggersResponseDto[]>
{
  constructor(private readonly triggersRepository: ITriggerRepository) {}

  async execute(data: FindSensorTriggersEntryDto): Promise<Result<FindSensorTriggersResponseDto[]>> {
    const triggers = await this.triggersRepository.getAllSensorTriggers(data.sensorId);

    return Result.success(
      triggers.map((trigger) => {
        return {
          id: trigger.Id,
          name: trigger.Name,
          creationDate: trigger.CreationDate,
          objectiveMeasure: trigger.ObjectiveMeasure,
          sensorId: trigger.SensorId,
          userId: trigger.UserId,
          comparison: trigger.Comparison,
          actuators: trigger.Actuators,
          objectiveHour: trigger.ObjectiveHour,
        };
      }),
    );
  }
  get name(): string {
    return this.constructor.name;
  }
}
