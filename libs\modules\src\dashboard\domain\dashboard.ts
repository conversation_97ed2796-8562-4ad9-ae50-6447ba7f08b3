import { Entity } from '@core/core';
import { DashboardTypesEnum } from './enum/dashboard-types.enum'

export class Dashboard extends Entity<string> {
  private sensorId: string;
  private userId: string;
  private type: DashboardTypesEnum;
  private position: number;

  private constructor(id: string, sensorId: string, position: number, userId: string, type: DashboardTypesEnum) {
    super(id);
    this.sensorId = sensorId;
    this.position = position;
    this.userId = userId;
    this.type = type;
  }

  get SensorId(): string {
    return this.sensorId;
  }

  get UserId(): string {
    return this.userId;
  }

  get Position(): number {
    return this.position;
  }
  
  get Type(): DashboardTypesEnum {
    return this.type;
  }

  changePosition (newPosition: number){
    this.position = newPosition
  }

  static create(id: string, sensorId: string, position: number, userId: string, type: DashboardTypesEnum): Dashboard {
    return new Dashboard(id, sensorId, position, userId, type);
  }
}
