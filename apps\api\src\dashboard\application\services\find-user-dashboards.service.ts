import { IApplicationService, Result } from '@core/core';
import { IDashboardRepository } from '@modules/modules/dashboard/domain/repository/dashboard-repository.interface';
import { FindUserDashboardsEntryDto } from '../dto/entry/find-user-dashboards.application.dto';
import { FindUserDashboardsResponseDto } from '../dto/response/find-user-dashboards-response.application.dto';

export class FindUserDashboardsApplicationService
  implements IApplicationService<FindUserDashboardsEntryDto, FindUserDashboardsResponseDto[]>
{
  constructor(private readonly dashboardsRepository: IDashboardRepository) {}

  async execute(data: FindUserDashboardsEntryDto): Promise<Result<FindUserDashboardsResponseDto[]>> {
    const dashboards = await this.dashboardsRepository.findDashboardsByUserId(data.userId, data.page, data.limit);

    return Result.success(
      dashboards.map((dashboard) => {
        return {
          id: dashboard.Id,
          userId: dashboard.UserId,
          sensorId: dashboard.SensorId,
          position: dashboard.Position,
          type: dashboard.Type,
        };
      }),
    );
  }
  get name(): string {
    return this.constructor.name;
  }
}
