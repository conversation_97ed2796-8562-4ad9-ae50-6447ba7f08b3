import { Body, Controller, Get, Post, Logger } from '@nestjs/common';

import { SignUpUserApplicationService } from 'apps/api/src/auth/application/services/sign-up-user-service.application.service';
import { JwtService } from '@nestjs/jwt';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { UseGuards } from '@nestjs/common/decorators/core/use-guards.decorator';

import { LogInUserEntryInfraDto } from './dto/entry/log-in-user-entry.dto';
import { SignUpUserEntryInfraDto } from './dto/entry/sign-up-user-entry.dto';
import { CurrentUserSwaggerResponseDto } from './dto/response/current-user-swagger-response.dto';
import { LogInUserSwaggerResponseDto } from './dto/response/log-in-user-swagger-response.dto';
import { SignUpUserSwaggerResponseDto } from './dto/response/sign-up-user-swagger-response.dto';
import { LogInUserInfraService } from '../services/log-in-user-service.infraestructure.service';

import {
  EncryptorBcrypt,
  ExceptionDecorator,
  GetUser,
  HttpExceptionHandler,
  IdGenerator,
  IEncryptor,
  IJwtGenerator,
  IPushSender,
  JwtAuthGuard,
  JwtGenerator,
  LoggingDecorator,
  NativeLogger,
  PerformanceDecorator,
  UuidGenerator,
} from '@core/core';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { User } from '@modules/modules/user/domain/user';
import { FirebaseNotifier } from '@core/core/infrastructure/notifier/firebase-notifier-singleton'

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  private readonly logger: Logger;
  private readonly uuidGenerator: IdGenerator<string>;
  private readonly tokenGenerator: IJwtGenerator<string>;
  private readonly encryptor: IEncryptor;

  constructor(
    private readonly userRepository: OdmUserRepository,
    jwtAuthService: JwtService,
  ) {
    this.logger = new Logger('AuthController');
    this.uuidGenerator = new UuidGenerator();
    this.tokenGenerator = new JwtGenerator(jwtAuthService);
    this.encryptor = new EncryptorBcrypt();
  }

  @Get('current')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ description: 'Obtener usuario actual', type: CurrentUserSwaggerResponseDto })
  @ApiBearerAuth()
  async currentUser(@GetUser() user: User) {
    return {
      id: user.Id,
      email: user.Email,
      name: user.Name,
      sensors: user.Sensors?.map((sensor) => {
        return {
          id: sensor.Id,
          name: sensor.Name,
          type: sensor.Type,
        };
      }),
      actuators: user.Actuators?.map((actuator) => {
        return {
          id: actuator.Id,
          name: actuator.Name,
          type: actuator.Type
        }
      }),
      triggers: user.Triggers?.map((trigger) => {
        return {
          id: trigger.Id,
          name: trigger.Name,
          creationDate: trigger.CreationDate,
          userId: trigger.UserId,
          comparison: trigger.Comparison,
          actuators: trigger.Actuators,
          objectiveMeasures: trigger.ObjectiveMeasure,
          sensorId: trigger.SensorId,
          objectiveHour: trigger.ObjectiveHour
        }
      }),
      notificationTokens: user.NotificationTokens,
    };
  }

  @Post('login')
  @ApiOkResponse({ description: 'Iniciar sesion de usuario', type: LogInUserSwaggerResponseDto })
  async logInUser(@Body() logInDto: LogInUserEntryInfraDto) {
    const data = { id: 'none', ...logInDto };
    const logInUserService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new LogInUserInfraService(this.userRepository, this.tokenGenerator, this.encryptor),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    return (await logInUserService.execute({ ...data, userId: data.id })).Value;
  }

  @Post('register')
  @ApiOkResponse({ description: 'Registrar un nuevo usuario en el sistema', type: SignUpUserSwaggerResponseDto })
  async signUpUser(@Body() signUpDto: SignUpUserEntryInfraDto) {
    const data = { id: 'none', ...signUpDto };

    const signUpApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new SignUpUserApplicationService(this.userRepository, this.uuidGenerator),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    const result = await signUpApplicationService.execute({
      userId: 'none',
      email: data.email,
      name: data.name,
      password: data.password,
    });

    const logInUserService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new LogInUserInfraService(this.userRepository, this.tokenGenerator, this.encryptor),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );

    const resultService = await logInUserService.execute({
      userId: 'none',
      email: data.email,
      password: data.password,
    });

    return resultService.Value ;
  }

  @Post('save-notification-token')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async addNotificationToken(@GetUser() user: User, @Body() data: { token: string}) {
    await this.userRepository.addNotificationTokenToUser(user.Id, data.token);
  }

  @Get('get-notification-tokens')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getNotificationTokens(@GetUser() user: User) {
    return await this.userRepository.getNotificationTokensForUser(user.Id);
  }

}
