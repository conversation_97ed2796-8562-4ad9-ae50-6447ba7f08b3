import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { OdmDashboardMapper } from './mappers/odm-mappers/odm-dashboard-mapper';
import { OdmDashboardRepository } from './repositories/odm-repositories/odm-dashboard-repository';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { DashboardSchema, OdmDashboardEntity } from './entities/odm-entities/odm-dashboard.entity'


@Module({
  imports: [
    UserEntityModule,
    MongooseModule.forFeature([{ name: OdmDashboardEntity.name, schema: DashboardSchema }]),
  ],
  providers: [
    OdmDashboardMapper,
    OdmDashboardRepository,
  ],
  exports: [OdmDashboardRepository],
})
export class DashboardEntityModule {}
