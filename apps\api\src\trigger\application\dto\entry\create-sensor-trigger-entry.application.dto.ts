import { ApplicationServiceEntryDto } from '@core/core';
import { ComparisonTypesEnum } from '@modules/modules/trigger/domain/enum/comparison-types.enum';
import { IActuatorAction } from '@modules/modules/trigger/domain/types/actuator-action.type';

export interface CreateSensorTriggerEntryDto extends ApplicationServiceEntryDto {
  name: string;
  objectiveMeasure: number[];
  sensorId: string;
  userId: string;
  comparison: ComparisonTypesEnum;
  actuators: IActuatorAction[];
}
