import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface';
import { Injectable } from '@nestjs/common';
import { OdmActuatorMapper } from '../../mappers/odm-mappers/odm-actuator-mapper';
import { OdmUserEntity } from '@modules/modules/user/infrastructure/entities/odm-entities/odm-user.entity';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Actuator } from '@modules/modules/actuator/domain/actuator';
import { Optional } from '@core/core/domain/optional/optional';
import { PaginationInfo } from '@core/core/infrastructure/pagination/interfaces/pagination.info'
import { makePaginationInfo } from '@core/core/infrastructure/pagination/helpers/pagination.factory'

@Injectable()
export class OdmActuatorRepository implements IActuatorRepository {
  constructor(
    @InjectModel(OdmUserEntity.name)
    private readonly userModel: Model<OdmUserEntity>,
    private readonly actuatorMapper: OdmActuatorMapper,
  ) {}
  
  async findActuatorById(userId: string, actuatorId: string): Promise<Optional<Actuator>> {
    const user = await this.userModel.findById(userId).populate('actuators').exec();
    if (!user) {
      return Optional.of<Actuator>(null);
    }
    const actuator = user.actuators.find((actuator) => actuator._id.toString() === actuatorId);
    if (!actuator) {
      return Optional.of<Actuator>(null);
    }
    return Optional.of(await this.actuatorMapper.fromPersistenceToDomain(actuator));
  }
  
  async findActuatorsByUserId(userId: string, page: number, limit: number): Promise<Actuator[]> {
    const user = await this.userModel.findById(userId).populate('actuators').exec();
    if (!user) {
      return [];
    }
    const actuators = user.actuators.slice((page - 1) * limit, page * limit);
    if (!actuators) {
      return [];
    }
    const domainActuators: Actuator[] = [];
    actuators.forEach(async (actuator) =>
      domainActuators.push(await this.actuatorMapper.fromPersistenceToDomain(actuator)),
    );
    return domainActuators;
  }

  async findManyPagination ( userId: string, page: number, limit: number ): Promise<PaginationInfo>
  {
    return this.userModel.aggregate([
      { $match: { _id: userId } },
      { $unwind: '$actuators' },
      { $count: 'total' },
    ]).then((result) => {
      const total: number = result[0]?.total || 0;
      return makePaginationInfo({ count: total, page, limit })
    });
  }
}
