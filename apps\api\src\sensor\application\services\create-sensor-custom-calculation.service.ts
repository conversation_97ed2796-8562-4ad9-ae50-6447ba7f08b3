import { IApplicationService, Result, IdGenerator } from '@core/core';
import { CreateSensorCustomCalculationEntryDto } from '../dto/entry/create-sensor-custom-calculation.application.dto';
import { CreateSensorCustomCalculationResponseDto } from '../dto/response/create-sensor-custom-calculation-response.application.dto';
import { CustomCalculation } from '@modules/modules/sensor/domain/custom-calculation';
import { ICalculation } from '@modules/modules/sensor/domain/types/calculation.type'
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface'

export class CreateSensorCustomCalculationApplicationService
  implements IApplicationService<CreateSensorCustomCalculationEntryDto, CreateSensorCustomCalculationResponseDto>
{
  private readonly uuidGenerator: IdGenerator<string>;

  constructor(uuidGenerator: IdGenerator<string>,
    private readonly sensorRepository: ISensorRepository,
  ) {
    this.uuidGenerator = uuidGenerator;
  }

  async execute(
    createSensorCustomCalculationEntryDto: CreateSensorCustomCalculationEntryDto
  ): Promise<Result<CreateSensorCustomCalculationResponseDto>> {
    const idCustomCalculation = await this.uuidGenerator.generateId();

    const create = CustomCalculation.create(
      idCustomCalculation,
      createSensorCustomCalculationEntryDto.sensorId,
      createSensorCustomCalculationEntryDto.calculations,
    );

    await this.sensorRepository.saveCustomCalculation(create);

    return Result.success({ response: 'Custom calculation created successfully' });
  }

  get name(): string {
    return this.constructor.name;
  }
}
