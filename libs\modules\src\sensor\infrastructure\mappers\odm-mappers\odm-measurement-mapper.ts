import { <PERSON><PERSON>ap<PERSON> } from '@core/core';
import { Model } from 'mongoose';
import { OdmMeasurementEntity } from '../../entities/odm-entities/odm-measurement.entity';
import { Measurement } from '@modules/modules/sensor/domain/measurement';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class OdmMeasurementMapper implements IMapper<Measurement, OdmMeasurementEntity> {
  constructor(
    @InjectModel(OdmMeasurementEntity.name)
    private readonly measurementModel: Model<OdmMeasurementEntity>,
  ) {}

  fromDomainToPersistence(domain: Measurement): Promise<OdmMeasurementEntity> {
    return new Promise(async (resolve, reject) => {
      const measurement = new this.measurementModel({
        _id: domain.Id,
        measure: domain.Measure,
        sensorId: domain.SensorId,
        userId: domain.UserId,
      });
      resolve(measurement);
    });
  }
  async fromPersistenceToDomain(measurement: OdmMeasurementEntity): Promise<Measurement> {
    return Measurement.create(
      measurement._id,
      measurement.creationDate,
      measurement.measure,
      measurement.sensorId,
      measurement.userId,
    );
  }
}
