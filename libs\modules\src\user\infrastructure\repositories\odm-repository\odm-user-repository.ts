import { Result } from '@core/core';
import { OdmUserEntity } from '../../entities/odm-entities/odm-user.entity';
import { Model } from 'mongoose';
import { UserNotFoundException } from '../../exceptions/user-not-found-exception';

import { OdmUserMapper } from '../../mappers/odm-mappers/odm-user-mapper';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { User } from '@modules/modules/user/domain/user';
import { Sensor } from '@modules/modules/sensor/domain/sensor';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Optional } from '@core/core/domain/optional/optional';
import { OdmTriggerEntity } from '@modules/modules/trigger/infrastructure/entities/odm-entities/odm-trigger.entity'
import { Actuator } from '@modules/modules/actuator/domain/actuator'

@Injectable()
export class OdmUserRepository implements IUserRepository {
  constructor(
    @InjectModel(OdmUserEntity.name)
    private readonly userModel: Model<OdmUserEntity>,
    @InjectModel(OdmTriggerEntity.name)
    private readonly triggerModel: Model<OdmTriggerEntity>,
    private readonly odmMapper: OdmUserMapper,
  ) {}

  async addActuatorToUser(userId: string, actuator: Actuator): Promise<void> {
    const user = await this.userModel.findOne({ _id: userId });
    user!.actuators.push({
      _id: actuator.Id,
      name: actuator.Name,
      type: actuator.Type,
    });
    await user!.save();
  }

  async userHasActuatorByName(userId: string, actuatorName: string): Promise<boolean> {
    const user = await this.userModel.findOne({ _id: userId });

    for (const userActuator of user!.actuators) {
      if (userActuator.name == actuatorName) {
        return true;
      }
    }
    return false;
  }

  async addSensorToUser(userId: string, sensor: Sensor): Promise<void> {
    const user = await this.userModel.findOne({ _id: userId });
    user!.sensors.push({
      _id: sensor.Id,
      name: sensor.Name,
      type: sensor.Type,
    });
    await user!.save();
  }

  async userHasSensorByName(userId: string, sensorName: string): Promise<boolean> {
    const user = await this.userModel.findOne({ _id: userId });

    for (const userSensor of user!.sensors) {
      if (userSensor.name == sensorName) {
        return true;
      }
    }
    return false;
  }

  async saveUser(user: User): Promise<void> {
    const userOdm = await this.odmMapper.fromDomainToPersistence(user);
    const newUserOdm = new this.userModel(userOdm);
    await newUserOdm.save();
  }

  async findUserById(id: string): Promise<Optional<User>> {
    const user = await this.userModel.findOne({ _id: id });
    let triggers = [];
    if (user) {
      triggers = await this.triggerModel.find({ userId: id });
    }
    return Optional.of(user ? await this.odmMapper.fromPersistenceToDomain(user, triggers) : null);
  }

  async findUserByEmail(email: string): Promise<Optional<User>> {
    const user = await this.userModel.findOne({ email: email });
    if (user) {
      return Optional.of(await this.odmMapper.fromPersistenceToDomain(user));
    }
    return Optional.of<User>(null);
  }

  async findAllUser(): Promise<User[]> {
    const users: OdmUserEntity[] = await this.userModel.find().exec();
    const domainUsers: User[] = [];
    users.forEach(async (user) => {
      domainUsers.push(await this.odmMapper.fromPersistenceToDomain(user));
    });
    return domainUsers;
  }

  async addNotificationTokenToUser(userId: string, token: string): Promise<void> {
    const user = await this.userModel.findOne({ _id: userId });
    if (!user!.notificationTokens) {
      user!.notificationTokens = [];
    }
    if (user!.notificationTokens.includes(token)) {
      return;
    }
    user!.notificationTokens.push(token);
    await user!.save();
  }

  async removeNotificationTokenFromUser(userId: string, token: string): Promise<void> {
    const user = await this.userModel.findOne({ _id: userId });
    user!.notificationTokens = user!.notificationTokens.filter((t) => t !== token);
    await user!.save();
  }

  async getNotificationTokensForUser(userId: string): Promise<string[]> {
    const user = await this.userModel.findOne({ _id: userId });
    return user!.notificationTokens;
  }
}
