import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { odmDataBaseProviders } from '@core/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CoreModule } from '@core/core/core.module';
import { UserModule } from './user/infrastructure/user.module';
import { SensorModule } from './sensor/infrastructure/sensor.module';
import { AuthModule } from './auth/infrastructure/auth.module';
import { TriggerModule } from './trigger/infrastructure/trigger.module';
import { ActuatorModule } from './actuators/infrastructure/actuator.module'
import { DashboardModule } from './dashboard/infrastructure/dashboard.module'

@Module({
  imports: [
    CoreModule,
    ConfigModule.forRoot({ isGlobal: true }),
    MongooseModule.forRoot(process.env.MONGO_DB!, { dbName: 'tank-monitor-db' }),
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    AuthModule,
    UserModule,
    SensorModule,
    TriggerModule,
    ActuatorModule,
    DashboardModule
  ],
  providers: [odmDataBaseProviders],
})
export class ApiModule {}
