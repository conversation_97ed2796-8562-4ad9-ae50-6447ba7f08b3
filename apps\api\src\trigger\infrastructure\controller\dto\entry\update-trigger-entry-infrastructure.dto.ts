import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString, Max, Min, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ComparisonTypesEnum } from '@modules/modules/trigger/domain/enum/comparison-types.enum';
import { IActuatorAction } from '@modules/modules/trigger/domain/types/actuator-action.type';

export class UpdateTriggerEntryInfrastructureDto {
  @ApiProperty({ description: 'Trigger name', example: 'Water Level Alert' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Comparison type', enum: ComparisonTypesEnum })
  @IsNotEmpty()
  comparison: ComparisonTypesEnum;

  @ApiProperty({ description: 'Actuator actions', type: 'array' })
  @IsArray()
  actuators: IActuatorAction[];

  // Sensor trigger fields
  @ApiProperty({ description: 'Sensor ID (for sensor triggers)', required: false })
  @IsOptional()
  @IsString()
  sensorId?: string;

  @ApiProperty({ description: 'Objective measure values (for sensor triggers)', required: false })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  objectiveMeasures?: number[];

  // Time trigger fields
  @ApiProperty({ description: 'Objective hour (0-23, for time triggers)', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(23)
  ObjectiveHour?: number;

  @ApiProperty({ description: 'Objective minute (0-59, for time triggers)', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(59)
  ObjectiveMinute?: number;

  @ApiProperty({ description: 'Objective days (0-6, for time triggers)', required: false })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Min(1, { each: true })
  @Max(7, { each: true })
  ObjectiveDays?: number[];
}
