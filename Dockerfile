FROM node:22.14.0-alpine as builder

<PERSON>NV NODE_ENV build

WORKDIR /home/<USER>

COPY package*.json ./
RUN npm ci

COPY . .

RUN npm run build

RUN npm prune --production

FROM node:22.14.0-alpine

ENV NODE_ENV production

USER node

WORKDIR /home/<USER>

COPY --from=builder --chown=node:node /home/<USER>/package*.json ./
COPY --from=builder --chown=node:node /home/<USER>/node_modules/ ./node_modules/
COPY --from=builder --chown=node:node /home/<USER>/dist/ ./dist/

EXPOSE 3333

CMD ["node", "dist/apps/api/main.js"]