import { NestFactory } from '@nestjs/core';
import { ApiModule } from './api.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { json } from 'express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(ApiModule);
  app.enableCors({
    origin: '*',
    credentials: true,
  });

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: false,
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  );
  app.use(json({ limit: '200mb' }));
  const config = new DocumentBuilder()
    .setTitle('TANK MONITOR API')
    .setDescription('Tank Monitor endpoints')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('/docs', app, document);
  const port = process.env.PORT ?? 3000;
  new Logger('Bootstrap').log(`Server running on port ${port}`);
  await app.listen(port);
}
bootstrap();
