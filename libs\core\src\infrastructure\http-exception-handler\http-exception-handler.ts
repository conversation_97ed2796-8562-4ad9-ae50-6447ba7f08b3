import {
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
  ForbiddenException,
  ConflictException,
  HttpException,
} from '@nestjs/common';
import { IExceptionHandler } from '../../application/exception-handler/exception-handler.interface';
import { Exception } from '@core/core/domain/exception/exception';

export class HttpExceptionHandler implements IExceptionHandler {
  HandleException(exception: Exception): void {
    let statusCode: number;

    if (exception.code.endsWith('NF')) {
      statusCode = 404;
    } else if (exception.code.endsWith('UN')) {
      statusCode = 401;
    } else {
      statusCode = 400;
    }

    throw new HttpException({ code: exception.code, message: exception.message, info: exception.info }, statusCode);

    /* switch (result.Error.code) {
      case 400:
        return this.BadRequest(msg, error);
      case 403:
        return this.Forbidden(msg, error);
      case 404:
        return this.NotFound(msg, error);
      case 409:
        return this.Conflict(msg, error);
      case 500:
        return this.InternalServerError(msg, error);
      default:
        return this.InternalServerError(msg, error);
    } */
  }

  private Conflict(msg: string, error?: any): void {
    throw new ConflictException(msg, error);
  }

  private BadRequest(msg: string, error?: any): void {
    throw new BadRequestException(msg, error);
  }

  private NotFound(msg: string, error?: any): void {
    throw new NotFoundException(msg, error);
  }

  public Forbidden(msg: string, error?: any): void {
    throw new ForbiddenException(msg, error);
  }

  private InternalServerError(msg: string, error?: any): void {
    throw new InternalServerErrorException(msg, error);
  }
}
