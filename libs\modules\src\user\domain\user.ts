import { Entity } from '@core/core';
import { Actuator } from '@modules/modules/actuator/domain/actuator'
import { Sensor } from '@modules/modules/sensor/domain/sensor'
import { Trigger } from '@modules/modules/trigger/domain/trigger'

export class User extends Entity<string> {
  private name: string;
  private email: string;
  private password: string;
  private sensors?: Sensor[];
  private actuators?: Actuator[];
  private triggers?: Trigger[];
  private notificationTokens?: string[]; 

  private constructor(id: string, name: string, email: string, password: string, sensors?: Sensor[], actuators?: Actuator[], triggers?: Trigger[], notificationTokens?: string[]) {
    super(id);
    this.name = name;
    this.email = email;
    this.password = password;
    this.sensors = sensors;
    this.actuators = actuators;
    this.triggers = triggers;
    this.notificationTokens = notificationTokens;
  }

  get Name(): string {
    return this.name;
  }

  get Email(): string {
    return this.email;
  }

  get Password(): string {
    return this.password;
  }

  get Sensors(): Sensor[] | undefined {
    return this.sensors;
  }

  get Actuators(): Actuator[] | undefined {
    return this.actuators;
  }

  get Triggers(): Trigger[] | undefined {
    return this.triggers;
  }

  get NotificationTokens(): string[] | undefined {
    return this.notificationTokens;
  }

  static create(id: string, name: string, email: string, password: string, sensors?: Sensor[], actuators?: Actuator[], triggers?: Trigger[], notificationTokens?: string[]): User {
    return new User(id, name, email, password, sensors, actuators, triggers, notificationTokens);
  }
}
