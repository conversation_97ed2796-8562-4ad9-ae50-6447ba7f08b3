import { InjectModel } from '@nestjs/mongoose';
import { OdmSensorTokenEntity } from '../../entities/odm-entities/odm-sensor-token.entity';
import { Model } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { Optional } from '@core/core/domain/optional/optional';

@Injectable()
export class OdmSensorTokenRepository {
  constructor(
    @InjectModel(OdmSensorTokenEntity.name)
    private readonly sensorTokenModel: Model<OdmSensorTokenEntity>,
  ) {}

  async store({ sensorId, token, userId }: { sensorId: string; token: string; userId: string }): Promise<void> {
    const sensorToken = new this.sensorTokenModel({
      token: token,
      creationDate: new Date(),
      sensorId: sensorId,
      userId: userId,
    });
    await sensorToken.save();
  }

  async validateToken(token: string): Promise<Optional<{ userId: string; sensorId: string }>> {
    const sensorToken = await this.sensorTokenModel.findOne({ token: token });
    if (sensorToken) {
      return Optional.of({ sensorId: sensorToken.sensorId, userId: sensorToken.userId });
    }
    return Optional.of<{ userId: string; sensorId: string }>(null);
  }

  async getToken(sensorId: string): Promise<Optional<string>> {
    const sensor = await this.sensorTokenModel.findOne({ sensorId: sensorId });
    if (sensor) {
      return Optional.of(sensor.token);
    }
    return Optional.of<string>(null);
  }
}
