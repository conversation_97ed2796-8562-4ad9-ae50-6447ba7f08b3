import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { Injectable } from '@nestjs/common';
import { OdmTriggerMapper } from '../../mappers/odm-mappers/odm-trigger-mapper';
import { OdmUserEntity } from '@modules/modules/user/infrastructure/entities/odm-entities/odm-user.entity';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Trigger } from '@modules/modules/trigger/domain/trigger';
import { Optional } from '@core/core/domain/optional/optional';
import { OdmTriggerEntity } from '../../entities/odm-entities/odm-trigger.entity';
import { PaginationInfo } from '@core/core/infrastructure/pagination/interfaces/pagination.info'
import { makePaginationInfo } from '@core/core/infrastructure/pagination/helpers/pagination.factory'

@Injectable()
export class OdmTriggerRepository implements ITriggerRepository {
  constructor(
    @InjectModel(OdmTriggerEntity.name)
    private readonly triggerModel: Model<OdmTriggerEntity>,
    private readonly triggerMapper: OdmTriggerMapper,
  ) {}

  async getAllTriggers (page: number, limit: number): Promise<Trigger[]>
  {
    const triggers = await this.triggerModel.find({}).skip((page - 1) * limit).limit(limit);
    return await Promise.all(triggers.map((trigger) => this.triggerMapper.fromPersistenceToDomain(trigger)));
  }

  async save(trigger: Trigger): Promise<void> {
    const triggerOdm = await this.triggerMapper.fromDomainToPersistence(trigger);
    const newTriggerOdm = new this.triggerModel(triggerOdm);
    await newTriggerOdm.save();
  }

  async getAllUserTriggers(userId: string, page: number, limit: number): Promise<Trigger[]> {
    const triggers = await this.triggerModel.find({ userId: userId }).skip((page - 1) * limit).limit(limit);
    return await Promise.all(triggers.map((trigger) => this.triggerMapper.fromPersistenceToDomain(trigger)));
  }

  async getAllSensorTriggers(sensorId: string): Promise<Trigger[]> {
    const triggers = await this.triggerModel.find({ sensorId: sensorId });
    return await Promise.all(triggers.map((trigger) => this.triggerMapper.fromPersistenceToDomain(trigger)));
  }

  async findManyPagination ( userId: string, page: number, limit: number ): Promise<PaginationInfo>
  {
    return this.triggerModel.aggregate([
      { $match: { userId: userId } },
      { $count: 'total' },
    ]).then((result) => {
      const total: number = result[0]?.total || 0;
      return makePaginationInfo({ count: total, page, limit });
    });
  }

  async findUserTriggerById(triggerId: string, userId: string): Promise<Optional<Trigger>> {
    const trigger = await this.triggerModel.findOne({ _id: triggerId, userId: userId });
    if (!trigger) {
      return Optional.of<Trigger>(null);
    }
    return Optional.of(await this.triggerMapper.fromPersistenceToDomain(trigger));
  }

  async deleteTrigger(triggerId: string): Promise<void> {
    await this.triggerModel.deleteOne({ _id: triggerId });
  }
  
  async updateTrigger(trigger: Trigger): Promise<void> {
    await this.triggerModel.updateOne({ _id: trigger.Id }, 
      { 
        name: trigger.Name, 
        sensorId: trigger.SensorId, 
        userId: trigger.UserId, 
        comparison: trigger.Comparison, 
        objectiveMeasures: trigger.ObjectiveMeasure, 
        actuators: trigger.Actuators, 
        objectiveHour: trigger.ObjectiveHour,
      }
    );
  }
}
