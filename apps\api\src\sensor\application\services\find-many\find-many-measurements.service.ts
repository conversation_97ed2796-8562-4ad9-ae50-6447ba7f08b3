import { IApplicationService, Result } from '@core/core';
import { FindManyMeasurementsEntryDto } from './dto/entry';
import { FindManyMeasurementsResponseDto } from './dto/response';
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface';
import { sensorNotFoundException } from '../../exceptions/not-found';
import { IMeasurementRepository } from '@modules/modules/sensor/domain/repository/measurement-repository.interface';
import { wrongDateOrderException } from '../../exceptions/wrong-date-order';

export class FindManyMeasurementsApplicationService
  implements IApplicationService<FindManyMeasurementsEntryDto, FindManyMeasurementsResponseDto[]>
{
  constructor(
    private readonly sensorRepository: ISensorRepository,
    private readonly measurementRepository: IMeasurementRepository,
  ) {}

  async execute(data: FindManyMeasurementsEntryDto): Promise<Result<FindManyMeasurementsResponseDto[]>> {
    if (data.startDate.getTime() > data.endDate.getTime()) {
      return Result.fail(wrongDateOrderException());
    }

    const sensor = await this.sensorRepository.findSensor(data.userId, data.sensorId);

    if (!sensor.isPresent()) {
      return Result.fail(sensorNotFoundException());
    }

    const measurements = await this.measurementRepository.findMany({
      sensorId: data.sensorId,
      startDate: data.startDate,
      endDate: data.endDate,
    });

    return Result.success(
      measurements.map((measurement) => {
        return {
          measurement: measurement.Measure,
          timestamp: measurement.CreationDate,
        };
      }),
    );
  }
  get name(): string {
    return this.constructor.name;
  }
}
