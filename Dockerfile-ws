FROM node:22.14.0-alpine as builder

ENV NODE_ENV build

WORKDIR /home/<USER>

COPY package*.json ./
RUN npm ci

COPY . .

RUN npm run build

RUN npm prune --production

FROM node:22.14.0-alpine

ENV NODE_ENV production

USER node

WORKDIR /home/<USER>

COPY --from=builder --chown=node:node /home/<USER>/package*.json ./
COPY --from=builder --chown=node:node /home/<USER>/node_modules/ ./node_modules/
COPY --from=builder --chown=node:node /home/<USER>/dist/ ./dist/

EXPOSE 4000

CMD ["node", "dist/apps/ws/main.js"]
