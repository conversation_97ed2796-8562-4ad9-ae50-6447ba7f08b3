import { DashboardTypesEnum } from '@modules/modules/dashboard/domain/enum/dashboard-types.enum'
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsString, IsUUID, Min } from 'class-validator';

export class CreateDashboardEntryInfraDto {
  @ApiProperty({ example: '425169e1-e2ce-43f0-ab60-864500b32da9' })
  @IsUUID()
  sensorId: string;

  @ApiProperty({ example: DashboardTypesEnum.MEASURE })
  @IsEnum(DashboardTypesEnum)
  type: DashboardTypesEnum;

}
