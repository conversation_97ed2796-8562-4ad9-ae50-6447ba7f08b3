import { IApplicationService, Result } from '@core/core';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface'
import { FindUserActuatorsEntryDto } from '../dto/entry/find-user-actuators.application.dto'
import { FindUserActuatorsResponseDto } from '../dto/response/find-user-actuators-response.application.dto'

export class FindUserActuatorsApplicationService
  implements IApplicationService<FindUserActuatorsEntryDto, FindUserActuatorsResponseDto[]>
{
  constructor(
    private readonly actuatorsRepository: IActuatorRepository,
  ) {}

  async execute(data: FindUserActuatorsEntryDto): Promise<Result<FindUserActuatorsResponseDto[]>> {
    const actuators = await this.actuatorsRepository.findActuatorsByUserId(data.userId, data.page, data.limit);

    return Result.success(
      actuators.map((actuator) => {
        return {
          id: actuator.Id,
          name: actuator.Name,
          type: actuator.Type
        };
      }),
    );
  }
  get name(): string {
    return this.constructor.name;
  }
}
