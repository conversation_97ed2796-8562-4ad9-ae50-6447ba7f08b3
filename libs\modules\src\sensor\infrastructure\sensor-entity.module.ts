import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MeasurementSchema, OdmMeasurementEntity } from './entities/odm-entities/odm-measurement.entity';
import { OdmMeasurementMapper } from './mappers/odm-mappers/odm-measurement-mapper';
import { OdmMeasurementRepository } from './repositories/odm-repositories/odm-measurement-repository';
import { OdmSensorMapper } from './mappers/odm-mappers/odm-sensor-mapper';
import { OdmSensorRepository } from './repositories/odm-repositories/odm-sensor-repository';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { OdmSensorTokenEntity, SensorTokenSchema } from './entities/odm-entities/odm-sensor-token.entity';
import { OdmSensorTokenRepository } from './repositories/odm-repositories/odm-sensor-token-repository';
import { CustomCalculationSchema, OdmCustomCalculationEntity } from './entities/odm-entities/odm-custom-calculation.entity'
import { OdmCustomCalculationMapper } from './mappers/odm-mappers/odm-custom-calculation-mapper'

@Module({
  imports: [
    UserEntityModule,
    MongooseModule.forFeature([{ name: OdmMeasurementEntity.name, schema: MeasurementSchema }]),
    MongooseModule.forFeature([{ name: OdmSensorTokenEntity.name, schema: SensorTokenSchema }]),
    MongooseModule.forFeature([{ name: OdmCustomCalculationEntity.name, schema: CustomCalculationSchema }]),
  ],
  providers: [
    OdmMeasurementMapper,
    OdmMeasurementRepository,
    OdmSensorMapper,
    OdmCustomCalculationMapper,
    OdmSensorRepository,
    OdmSensorTokenRepository,
  ],
  exports: [MongooseModule, OdmMeasurementRepository, OdmSensorRepository, OdmSensorTokenRepository],
})
export class SensorEntityModule {}
