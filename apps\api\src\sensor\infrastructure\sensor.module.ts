import { SensorEntityModule } from '@modules/modules/sensor/infrastructure/sensor-entity.module';
import { Module } from '@nestjs/common';
import { SensorController } from './controller/sensor.controller';
import { AuthModule } from '../../auth/infrastructure/auth.module';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { CoreModule } from '@core/core/core.module';

@Module({
  imports: [SensorEntityModule, AuthModule, UserEntityModule, CoreModule],
  controllers: [SensorController],
})
export class SensorModule {}
