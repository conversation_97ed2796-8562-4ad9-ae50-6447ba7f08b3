import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { OdmActuatorMapper } from './mappers/odm-mappers/odm-actuator-mapper';
import { OdmActuatorRepository } from './repositories/odm-repositories/odm-actuator-repository';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { OdmActuatorTokenRepository } from './repositories/odm-repositories/odm-actuator-token-repository'
import { ActuatorTokenSchema, OdmActuatorTokenEntity } from './entities/odm-entities/odm-actuator-token.entity'


@Module({
  imports: [
    UserEntityModule,
    MongooseModule.forFeature([{ name: OdmActuatorTokenEntity.name, schema: ActuatorTokenSchema }]),
  ],
  providers: [
    OdmActuatorMapper,
    OdmActuatorRepository,
    OdmActuatorTokenRepository,
  ],
  exports: [OdmActuatorRepository, OdmActuatorTokenRepository],
})
export class ActuatorEntityModule {}
