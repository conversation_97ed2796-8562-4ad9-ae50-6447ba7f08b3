import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { OdmTriggerEntity, TriggerSchema } from './entities/odm-entities/odm-trigger.entity'
import { OdmTriggerMapper } from './mappers/odm-mappers/odm-trigger-mapper'
import { OdmTriggerRepository } from './repositories/odm-repositories/odm-trigger-repository'


@Module({
  imports: [
    MongooseModule.forFeature([{ name: OdmTriggerEntity.name, schema: TriggerSchema }]),
  ],
  providers: [
    OdmTriggerMapper,
    OdmTriggerRepository,
    
  ],
  exports: [MongooseModule, OdmTriggerRepository],
})
export class TriggerEntityModule {}
