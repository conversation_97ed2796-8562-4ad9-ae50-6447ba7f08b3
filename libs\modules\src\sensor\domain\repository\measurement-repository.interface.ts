import { Measurement } from '../measurement';
import { Optional } from '@core/core/domain/optional/optional';

export interface IMeasurementRepository {
  save(measurement: Measurement): Promise<void>;
  findMany(data: { sensorId: string; startDate: Date; endDate: Date }): Promise<Measurement[]>;
  getLastMeasurement(sensorId: string): Promise<Optional<Measurement>>;
  getAllMeasurements(sensorId: string): Promise<Measurement[]>;
}
