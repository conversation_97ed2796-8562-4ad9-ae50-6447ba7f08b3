import { IApplicationService, Result, IdGenerator } from '@core/core';
import { DeleteSensorEntryDto } from '../dto/entry/delete-sensor.application.dto';
import { DeleteSensorResponseDto } from '../dto/response/delete-sensor-response.application.dto';
import { IMessageSender } from '@core/core/application/messages-sender/message-sender.interface'
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface'
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface'
import { sensorNotFoundException } from '../exceptions/not-found'
import { IEventEmitter } from '@core/core/application/event-emitter/event-emitter.interface'

export class DeleteSensorApplicationService
  implements IApplicationService<DeleteSensorEntryDto, DeleteSensorResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly sensorRepository: ISensorRepository;
  private readonly messageSender: IMessageSender;
  private readonly eventEmitter: IEventEmitter;

  constructor(userRepository: IUserRepository, messageSender: IMessageSender, sensorRepository: ISensorRepository, eventEmitter: IEventEmitter) {
    this.messageSender = messageSender;
    this.userRepository = userRepository;
    this.sensorRepository = sensorRepository;
    this.eventEmitter = eventEmitter;
  }

  async execute(deleteSensorDto: DeleteSensorEntryDto): Promise<Result<DeleteSensorResponseDto>> {
    const sensor = await this.sensorRepository.findSensor(deleteSensorDto.userId, deleteSensorDto.sensorId);
    if (!sensor.isPresent()) return Result.fail(sensorNotFoundException());
    await this.userRepository.deleteSensorFromUser(deleteSensorDto.userId, deleteSensorDto.sensorId);

    this.messageSender.emit('delete-sensor/' + deleteSensorDto.sensorId, JSON.stringify({sensor: deleteSensorDto.sensorId}));
    const results = await this.eventEmitter.emitAsync('sensor.deleted', {sensorId: deleteSensorDto.sensorId, userId: deleteSensorDto.userId });
    return Result.success<DeleteSensorResponseDto>({ response: 'sensor eliminado con éxito' });
  }

  get name(): string {
    return this.constructor.name;
  }
}
