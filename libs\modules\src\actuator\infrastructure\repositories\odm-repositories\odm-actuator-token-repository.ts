import { InjectModel } from '@nestjs/mongoose';
import { OdmActuatorTokenEntity } from '../../entities/odm-entities/odm-actuator-token.entity';
import { Model } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { Optional } from '@core/core/domain/optional/optional';

@Injectable()
export class OdmActuatorTokenRepository {
  constructor(
    @InjectModel(OdmActuatorTokenEntity.name)
    private readonly actuatorTokenModel: Model<OdmActuatorTokenEntity>,
  ) {}

  async store({ actuatorId, token, userId }: { actuatorId: string; token: string; userId: string }): Promise<void> {
    const actuatorToken = new this.actuatorTokenModel({
      token: token,
      creationDate: new Date(),
      actuatorId: actuatorId,
      userId: userId,
    });
    await actuatorToken.save();
  }

  async validateToken(token: string): Promise<Optional<{ userId: string; actuatorId: string }>> {
    const actuatorToken = await this.actuatorTokenModel.findOne({ token: token });
    if (actuatorToken) {
      return Optional.of({ actuatorId: actuatorToken.actuatorId, userId: actuatorToken.userId });
    }
    return Optional.of<{ userId: string; actuatorId: string }>(null);
  }

  async getToken(actuatorId: string): Promise<Optional<string>> {
    const actuator = await this.actuatorTokenModel.findOne({ actuatorId: actuatorId });
    if (actuator) {
      return Optional.of(actuator.token);
    }
    return Optional.of<string>(null);
  }
}
