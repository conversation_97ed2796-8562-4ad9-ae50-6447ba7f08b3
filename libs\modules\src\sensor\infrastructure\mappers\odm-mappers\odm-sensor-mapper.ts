import { IMapper } from '@core/core';
import { Model } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { OdmSensorEntity } from '../../entities/odm-entities/odm-sensor.entity';
import { Sensor } from '@modules/modules/sensor/domain/sensor';

@Injectable()
export class OdmSensorMapper implements IMapper<Sensor, OdmSensorEntity> {
  private readonly sensorModel = Model<OdmSensorEntity>;

  fromDomainToPersistence(domain: Sensor): Promise<OdmSensorEntity> {
    return new Promise(async (resolve, reject) => {
      const sensor = new this.sensorModel({
        _id: domain.Id,
        name: domain.Name,
        type: domain.Type,
      });
      resolve(sensor);
    });
  }
  async fromPersistenceToDomain(measurement: OdmSensorEntity): Promise<Sensor> {
    return Sensor.create(measurement._id, measurement.name, measurement.type);
  }
}
