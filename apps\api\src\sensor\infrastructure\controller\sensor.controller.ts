import { Body, Controller, Post, Logger, Inject, Get, Query, Param, ParseUUI<PERSON>ipe, Delete, Put, NotFoundException } from '@nestjs/common';

import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { UseGuards } from '@nestjs/common/decorators/core/use-guards.decorator';

import {
  ExceptionDecorator,
  GetUser,
  HttpExceptionHandler,
  IdGenerator,
  JwtAuthGuard,
  LoggingDecorator,
  NativeLogger,
  PerformanceDecorator,
  UuidGenerator,
} from '@core/core';

import { User } from '@modules/modules/user/domain/user';
import { createSensorSwaggerResponseDto } from './dto/response/create-sensor-swagger-response.dto';
import { CreateSensorEntryInfraestructureDto } from './dto/entry/create-sensor-entry-infrastructure.dto';
import { CreateSensorApplicationService } from '../../application/services/create-sensor.application.service';
import { NestEventEmitter } from '@core/core/infrastructure/event-emitter/nest-event-emitter';
import { FindManyMeasurementsApplicationService } from '../../application/services/find-many/find-many-measurements.service';
import { OdmSensorRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-sensor-repository';
import { OdmMeasurementRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-measurement-repository';
import { OdmSensorTokenRepository } from '@modules/modules/sensor/infrastructure/repositories/odm-repositories/odm-sensor-token-repository';
import { TenAlphanumericCodeGenerator } from '@core/core/infrastructure/code-generator/ten-alphanumeric-code-generator';
import * as bcrypt from 'bcryptjs';
import { FindOneSensorApplicationService } from '../../application/services/find-one/find-one-sensor.service';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { sensorNotFoundException } from '../../application/exceptions/not-found';
import { makeExceptionFactory } from '@core/core/domain/exception/exception';
import { FindManyMeasurementsInfraestructureDto } from './dto/entry/find-many-measurements.dto';
import { DeleteSensorApplicationService } from '../../application/services/delete-sensor.application.service'
import { MqttService } from '@core/core/infrastructure/mqtt/services/mqtt.service'
import { UpdateSensorNameApplicationService } from '../../application/services/update-sensor-name.application.service';
import { UpdateSensorNameEntryInfrastructureDto } from './dto/entry/update-sensor-name-entry-infrastructure.dto';
import { UpdateSensorNameSwaggerResponseDto } from './dto/response/update-sensor-name-swagger-response.dto';
import { CreateSensorCustomCalculationApplicationService } from '../../application/services/create-sensor-custom-calculation.service'
import { CreateSensorCustomCalculationEntryInfrastructureDto } from './dto/entry/create-sensor-custom-calculation-entry-infrastructure.dto'
import { createSensorCustomCalculationSwaggerResponseDto } from './dto/response/create-sensor-custom-calculation-swagger-response.dto'

@ApiTags('Sensor')
@Controller('sensor')
export class SensorController {
  private readonly logger: Logger;
  private readonly uuidGenerator: IdGenerator<string>;

  constructor(
    @Inject() private readonly eventEmitter: NestEventEmitter,
    private readonly userRepository: OdmUserRepository,
    private readonly sensorRepository: OdmSensorRepository,
    private readonly measurementRepository: OdmMeasurementRepository,
    private readonly sensorTokenRepository: OdmSensorTokenRepository,
    @Inject() private readonly messageSender: MqttService,
  ) {
    this.logger = new Logger('SensorController');
    this.uuidGenerator = new UuidGenerator();
  }

  @Get('many/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findManyMeasurements(
    @GetUser() user: User,
    @Query() query: FindManyMeasurementsInfraestructureDto,
    @Param('id', ParseUUIDPipe) id: string,
  ) {
    const result = await new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindManyMeasurementsApplicationService(this.sensorRepository, this.measurementRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      userId: user.Id,
      sensorId: id,
      startDate: new Date(query.startDate),
      endDate: new Date(query.endDate),
    });

    return result.Value;
  }

  @Post('add-sensor-user')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ description: 'Añadir un sensor a un usuario', type: createSensorSwaggerResponseDto })
  @ApiBearerAuth()
  async addSensorToUser(@GetUser() user: User, @Body() createSensorDto: CreateSensorEntryInfraestructureDto) {
    const createSensorApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new CreateSensorApplicationService(this.uuidGenerator, this.eventEmitter),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );

    const result = await createSensorApplicationService.execute({
      userId: user.Id,
      name: createSensorDto.name,
      type: createSensorDto.type,
    });

    const sensorId = result.Value.sensorCreatedEvent.sensor.Id;

    const sensorToken = await bcrypt.hash(TenAlphanumericCodeGenerator.generate(), 3);

    await this.sensorTokenRepository.store({
      sensorId,
      token: sensorToken,
      userId: user.Id,
    });

    return { sensor: result.Value.sensorCreatedEvent.sensor, sensorToken };
  }

  @Get('one/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findOneSensor(@GetUser() user: User, @Param('id', ParseUUIDPipe) id: string) {
    const result = await new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new FindOneSensorApplicationService(this.userRepository, this.sensorRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    ).execute({
      sensorId: id,
      userId: user.Id,
    });

    return result.Value;
  }

  @Get('token/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getSensorToken(@GetUser() user: User, @Param('id', ParseUUIDPipe) id: string) {
    const exceptionHandler = new HttpExceptionHandler();

    const sensor = user.Sensors?.find((s) => s.Id === id);

    if (!sensor) {
      exceptionHandler.HandleException(sensorNotFoundException());
    }

    const tokenResult = await this.sensorTokenRepository.getToken(id);

    if (!tokenResult.isPresent()) {
      const tokenNotFoundException = makeExceptionFactory({
        code: 'ST-NF',
        message: 'Token not found',
      });
      exceptionHandler.HandleException(tokenNotFoundException());
    }

    return {
      token: tokenResult.get(),
    };
  }

  @Delete('delete/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Eliminar un sensor', type: String })
  async deleteSensor(@GetUser() user: User, @Param('id', ParseUUIDPipe) id: string) {
    const deleteApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new DeleteSensorApplicationService(this.userRepository, this.messageSender, this.sensorRepository, this.eventEmitter),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );
    const result = await deleteApplicationService.execute({
      userId: user.Id,
      sensorId: id,
    });

    return result.Value;
  }

  @Put('update-name/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Update sensor name', type: UpdateSensorNameSwaggerResponseDto })
  async updateSensorName(
    @GetUser() user: User,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateSensorDto: UpdateSensorNameEntryInfrastructureDto
  ) {
    const updateApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new UpdateSensorNameApplicationService(this.userRepository, this.sensorRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );

    const result = await updateApplicationService.execute({
      userId: user.Id,
      sensorId: id,
      name: updateSensorDto.name,
    });

    return result.Value;
  }

  @Post('custom-calculation')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Create custom calculation', type: createSensorCustomCalculationSwaggerResponseDto })
  async createCustomCalculation(
    @GetUser() user: User,
    @Body() createCustomCalculationDto: CreateSensorCustomCalculationEntryInfrastructureDto
  ) {
    const sensor = await this.sensorRepository.findSensor(user.Id, createCustomCalculationDto.sensorId);
    if (!sensor.isPresent()) {
      throw new NotFoundException('Sensor not found');
    }
    const createCustomCalculationApplicationService = new ExceptionDecorator(
      new LoggingDecorator(
        new PerformanceDecorator(
          new CreateSensorCustomCalculationApplicationService(this.uuidGenerator, this.sensorRepository),
          new NativeLogger(this.logger),
        ),
        new NativeLogger(this.logger),
      ),
      new HttpExceptionHandler(),
    );

    const result = await createCustomCalculationApplicationService.execute({
      userId: user.Id,
      sensorId: createCustomCalculationDto.sensorId,
      calculations: createCustomCalculationDto.calculations,
    });

    return result.Value;
  }
}
