import { DashboardTypesEnum } from '@modules/modules/dashboard/domain/enum/dashboard-types.enum'
import { ApiProperty } from '@nestjs/swagger';

export class FindUserDashboardsSwaggerResponseDto {
  @ApiProperty({
    example: '425169e1-e2ce-43f0-ab60-864500b32da9',
  })
  id: string;

  @ApiProperty({
    example: '425169e1-e2ce-43f0-ab60-864500b32da9',
  })
  sensorId: string;

  @ApiProperty({
    example: '425169e1-e2ce-43f0-ab60-864500b32da9',
  })
  userId: string;

  @ApiProperty({
    example: 2,
  })
  position: number;

  @ApiProperty({
    example: DashboardTypesEnum.MEASURE,
  })
  type: DashboardTypesEnum;

  @ApiProperty({ example: [{ measurement: 55.2, timestamp: new Date() }] })
  measurements: {
    measurement: number;
    timestamp: Date;
  }[];
}
