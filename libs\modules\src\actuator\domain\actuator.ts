import { Entity } from '@core/core';
import { ActuatorTypesEnum } from './enums/actuator-types.enum'

export class Actuator extends Entity<string> {
  private name: string;
  private type: ActuatorTypesEnum;

  private constructor(id: string, name: string, type: ActuatorTypesEnum) {
    super(id);
    this.name = name;
    this.type = type;
  }

  get Name(): string {
    return this.name;
  }

  get Type(): ActuatorTypesEnum {
    return this.type;
  }

  static create(id: string, name: string, type: ActuatorTypesEnum): Actuator {
    return new Actuator(id, name, type);
  }
}
