{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/api/tsconfig.app.json"}, "monorepo": true, "root": "apps/api", "projects": {"api": {"type": "application", "root": "apps/api", "entryFile": "main", "sourceRoot": "apps/api/src", "compilerOptions": {"tsConfigPath": "apps/api/tsconfig.app.json"}}, "auth": {"type": "library", "root": "libs/auth", "entryFile": "index", "sourceRoot": "libs/auth/src", "compilerOptions": {"tsConfigPath": "libs/auth/tsconfig.lib.json"}}, "core": {"type": "library", "root": "libs/core", "entryFile": "index", "sourceRoot": "libs/core/src", "compilerOptions": {"tsConfigPath": "libs/core/tsconfig.lib.json"}}, "modules": {"type": "library", "root": "libs/modules", "entryFile": "index", "sourceRoot": "libs/modules/src", "compilerOptions": {"tsConfigPath": "libs/modules/tsconfig.lib.json"}}, "ws": {"type": "application", "root": "apps/ws", "entryFile": "main", "sourceRoot": "apps/ws/src", "compilerOptions": {"tsConfigPath": "apps/ws/tsconfig.app.json"}}}}