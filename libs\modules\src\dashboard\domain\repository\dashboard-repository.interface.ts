import { Optional } from '@core/core/domain/optional/optional';
import { Dashboard } from '../dashboard'
import { PaginationInfo } from '@core/core/infrastructure/pagination/interfaces/pagination.info'

export interface IDashboardRepository {
  saveDashboard(dashboard: Dashboard): Promise<void>;
  updateDashboardPosition(id: string, position: number): Promise<void>;
  deleteDashboard(dashboardId: string): Promise<void>;
  findDashboardById(dashboardId: string): Promise<Optional<Dashboard>>;
  findDashboardsByUserId(userId: string, page: number, limit: number): Promise<Dashboard[]>;
  findManyPagination(userId: string, page: number, limit: number): Promise<PaginationInfo>;
}
