import { <PERSON><PERSON>apper } from '@core/core';
import { Model } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { Actuator } from '@modules/modules/actuator/domain/actuator';
import { OdmActuatorEntity } from '../../entities/odm-entities/odm-actuator.entity'

@Injectable()
export class OdmActuatorMapper implements IMapper<Actuator, OdmActuatorEntity> {
  private readonly actuatorModel = Model<OdmActuatorEntity>;

  fromDomainToPersistence(domain: Actuator): Promise<OdmActuatorEntity> {
    return new Promise(async (resolve, reject) => {
      const actuator = new this.actuatorModel({
        _id: domain.Id,
        name: domain.Name,
        type: domain.Type,
      });
      resolve(actuator);
    });
  }
  async fromPersistenceToDomain(actuator: OdmActuatorEntity): Promise<Actuator> {
    return Actuator.create(actuator._id, actuator.name, actuator.type);
  }
}
