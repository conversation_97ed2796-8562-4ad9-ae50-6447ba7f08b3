import { IApplicationService, Result } from '@core/core';

import { ITriggerRepository } from '@modules/modules/trigger/domain/repository/trigger-repository.interface';
import { DeleteActuatorFromTriggersEntryDto } from '../dto/entry/delete-actuator-from-triggers-entry.application.dto'

export class DeleteActuatorFromTriggersApplicationService
  implements IApplicationService<DeleteActuatorFromTriggersEntryDto, string>
{
  private readonly triggerRepository: ITriggerRepository;

  constructor(
    triggerRepository: ITriggerRepository
  ) {
    this.triggerRepository = triggerRepository;
  }

  async execute(deleteTriggerEntry: DeleteActuatorFromTriggersEntryDto): Promise<Result<string>> {
    await this.triggerRepository.deleteActuatorFromTriggers(deleteTriggerEntry.actuatorId, deleteTriggerEntry.userId);
    return Result.success<string>('triggers eliminados con éxito');
  }

  get name(): string {
    return this.constructor.name;
  }
}
