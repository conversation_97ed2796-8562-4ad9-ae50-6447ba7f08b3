import { IApplicationService, IEncryptor, Result, IJwtGenerator } from '@core/core';

import { LogInEntryDto } from './dto/entry/log-in-entry.infraestructure.dto';
import { LogInResponseDto } from './dto/response/log-in-response.dto';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { invalidCredentialsException } from '../../application/exceptions/invalid-credentials.exception';

export class LogInUserInfraService implements IApplicationService<LogInEntryDto, LogInResponseDto> {
  private readonly userRepository: IUserRepository;
  private readonly tokenGenerator: IJwtGenerator<string>;
  private readonly encryptor: IEncryptor;

  constructor(userRepository: IUserRepository, tokenGenerator: IJwtGenerator<string>, encryptor: IEncryptor) {
    this.userRepository = userRepository;
    (this.tokenGenerator = tokenGenerator), (this.encryptor = encryptor);
  }

  async execute(logInDto: LogInEntryDto): Promise<Result<LogInResponseDto>> {
    const userResult = await this.userRepository.findUserByEmail(logInDto.email);
    if (!userResult.isPresent()) {
      return Result.fail<LogInResponseDto>(invalidCredentialsException());
    }
    const user = userResult.get();
    const checkPassword = await this.encryptor.comparePlaneAndHash(logInDto.password, user.Password);
    if (!checkPassword) return Result.fail(invalidCredentialsException());
    const token = this.tokenGenerator.generateJwt(user.Id);
    const answer = {
      token: token,
      user: {
        email: user.Email,
        name: user.Name,
      },
    };
    return Result.success(answer);
  }

  get name(): string {
    return this.constructor.name;
  }
}
