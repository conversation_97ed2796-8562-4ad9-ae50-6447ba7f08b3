import { <PERSON><PERSON><PERSON><PERSON> } from '@core/core';
import { Model } from 'mongoose';
import { OdmTriggerEntity } from '../../entities/odm-entities/odm-trigger.entity';
import { Trigger } from '@modules/modules/trigger/domain/trigger';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class OdmTriggerMapper implements IMapper<Trigger, OdmTriggerEntity> {
  constructor(
    @InjectModel(OdmTriggerEntity.name)
    private readonly triggerModel: Model<OdmTriggerEntity>,
  ) {}

  fromDomainToPersistence(domain: Trigger): Promise<OdmTriggerEntity> {
    return new Promise(async (resolve, reject) => {
      const trigger = new this.triggerModel({
        _id: domain.Id,
        name: domain.Name,
        sensorId: domain.SensorId,
        userId: domain.UserId,
        comparison: domain.Comparison,
        objectiveMeasures: domain.ObjectiveMeasure,
        actuators: domain.Actuators,
        objectiveHour: domain.ObjectiveHour,
      });
      resolve(trigger);
    });
  }
  async fromPersistenceToDomain(trigger: OdmTriggerEntity): Promise<Trigger> {
    return Trigger.create(
      trigger._id,
      trigger.name,
      trigger.creationDate,
      trigger.userId,
      trigger.comparison,
      trigger.actuators,
      trigger.objectiveMeasures,
      trigger.sensorId,
      trigger.objectiveHour,
    );
  }
}
