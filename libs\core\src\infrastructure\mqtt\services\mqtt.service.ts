import { Inject, Injectable } from '@nestjs/common';
import { MqttClient, connect } from 'mqtt';
import { NestEventEmitter } from '../../event-emitter/nest-event-emitter'
import { MqttTopics } from '../../../application/messages-sender/enum/mqtt-topics.enum'
import { IMessageSender } from '@core/core/application/messages-sender/message-sender.interface'

@Injectable()
export class MqttService implements IMessageSender{
  public readonly mqtt: MqttClient;

  constructor(@Inject() private readonly eventEmitter: NestEventEmitter) {
    this.mqtt = connect(process.env.EMQX_URL!, {
      clientId: process.env.EMQX_CLIENT_ID || undefined,
      clean: true,
      connectTimeout: 10,
      username: process.env.EMQX_USERNAME,
      password: process.env.EMQX_PASSWORD,
      reconnectPeriod: 10,
    });

    this.mqtt.on('connect', () => {
      console.log('Connected to MQTT server');
    });

    for (const topic of Object.values(MqttTopics)) {
      this.mqtt.subscribe(topic, { qos: 1 });
    }
    
    this.mqtt.on('message', function (topic, message) {
        eventEmitter.emit(topic, JSON.parse(message.toString()));
    });
  }

  public emit(topic: string, message: string): void {
    this.mqtt.publish(topic, message, { qos: 1, retain: true}, (error) => {
      if (error) {
        console.error('Error publishing message:', error);
      } else {
        console.log(`Message published to ${topic}:`, message);
      }
    });
  }

  public async getLastMessage(topic: string): string {
    
  }
}
