import { Inject, Injectable } from '@nestjs/common';
import { MqttClient, connect } from 'mqtt';
import { NestEventEmitter } from '../../event-emitter/nest-event-emitter'
import { MqttTopics } from '../../../application/messages-sender/enum/mqtt-topics.enum'
import { IMessageSender } from '@core/core/application/messages-sender/message-sender.interface'

@Injectable()
export class MqttService implements IMessageSender{
  public readonly mqtt: MqttClient;

  constructor(@Inject() private readonly eventEmitter: NestEventEmitter) {
    const baseClientId = process.env.EMQX_CLIENT_ID || 'tank-monitor';
    const uniqueClientId = `${baseClientId}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    this.mqtt = connect(process.env.EMQX_URL!, {
      clientId: uniqueClientId,
      clean: true,
      connectTimeout: 10,
      username: process.env.EMQX_USERNAME,
      password: process.env.EMQX_PASSWORD,
      reconnectPeriod: 10,
    });

    this.mqtt.on('connect', () => {
      console.log('Connected to MQTT server with clientId: ' + uniqueClientId);
    });

    for (const topic of Object.values(MqttTopics)) {
      this.mqtt.subscribe(topic, { qos: 1 });
    }
    
    this.mqtt.on('message', function (topic, message) {
        eventEmitter.emit(topic, JSON.parse(message.toString()));
    });
  }

  public emit(topic: string, message: string): void {
    this.mqtt.publish(topic, message, { qos: 1, retain: true}, (error) => {
      if (error) {
        console.error('Error publishing message:', error);
      } else {
        console.log(`Message published to ${topic}:`, message);
      }
    });
  }

  public async getLastMessage(topic: string): Promise<string> {
    return new Promise((resolve, reject) => {
      // Create a temporary subscription to get the retained message
      const tempClientId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const tempClient = connect(process.env.EMQX_URL!, {
        clientId: tempClientId,
        clean: true,
        connectTimeout: 10,
        username: process.env.EMQX_USERNAME,
        password: process.env.EMQX_PASSWORD,
      });

      let messageReceived = false;
      const timeout = setTimeout(() => {
        if (!messageReceived) {
          tempClient.end();
          reject(new Error(`Timeout: No retained message found for topic ${topic}`));
        }
      }, 5000); // 5 second timeout

      tempClient.on('connect', () => {
        // Subscribe to the topic to receive the retained message
        tempClient.subscribe(topic, { qos: 1 }, (error) => {
          if (error) {
            clearTimeout(timeout);
            tempClient.end();
            reject(error);
          }
        });
      });

      tempClient.on('message', (receivedTopic, message) => {
        if (receivedTopic === topic) {
          messageReceived = true;
          clearTimeout(timeout);
          tempClient.end();
          resolve(message.toString());
        }
      });

      tempClient.on('error', (error) => {
        clearTimeout(timeout);
        tempClient.end();
        reject(error);
      });
    });
  }

  public subscribe(topic: string, callback: (message: string) => void): void {
    this.mqtt.subscribe(topic, { qos: 1 });
    this.mqtt.on('message', (receivedTopic, message) => {
      if (receivedTopic === topic) {
        callback(message.toString());
      }
    });
  }
}
