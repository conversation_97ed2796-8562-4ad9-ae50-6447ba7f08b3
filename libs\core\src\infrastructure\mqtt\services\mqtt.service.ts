import { Inject, Injectable, Logger } from '@nestjs/common';
import { MqttClient, connect } from 'mqtt';
import { NestEventEmitter } from '../../event-emitter/nest-event-emitter'
import { MqttTopics } from '../../../application/messages-sender/enum/mqtt-topics.enum'
import { IMessageSender } from '@core/core/application/messages-sender/message-sender.interface'

@Injectable()
export class MqttService implements IMessageSender{
  public readonly mqtt: MqttClient;
  private logger = new Logger(MqttService.name);

  constructor(@Inject() private readonly eventEmitter: NestEventEmitter) {
    const baseClientId = process.env.EMQX_CLIENT_ID || 'tank-monitor';
    const uniqueClientId = `${baseClientId}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    console.log('Connecting to MQTT server ' + process.env.EMQX_URL! + ' with clientId: ' + uniqueClientId);
    this.mqtt = connect(process.env.EMQX_URL!, {
      protocolId: 'MQTT',
      protocolVersion: 5,
      clientId: uniqueClientId,
      clean: true,
      connectTimeout: 5000,
      username: process.env.EMQX_USERNAME,
      password: process.env.EMQX_PASSWORD,
      reconnectPeriod: 1000,
    });
    this.mqtt.on('error', (error) => {
      console.error('MQTT error:', error);
    });
    this.mqtt.on('connect', () => {
      console.log('Connected to MQTT server with clientId: ' + uniqueClientId);
    });
    
    // this.mqtt.on('message', function (topic, message) {
    //     console.log('Received message from ' + topic + ': ' + message.toString());
    //     eventEmitter.emit(topic, JSON.parse(message.toString()));
    // });
  }

  public emit(topic: string, message: string, retain: boolean = true): void {
    this.mqtt.publish(topic, message, { qos: 1, retain: retain}, (error) => {
      if (error) {
        console.error('Error publishing message:', error);
      } else {
        console.log(`Message published to ${topic}:`, message);
      }
    });
  }

  public async getLastMessage(topic: string): Promise<string> {
    return new Promise((resolve, reject) => {
      // Create a temporary subscription to get the retained message
      const tempClientId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const tempClient = connect(process.env.EMQX_URL!, {
        clientId: tempClientId,
        protocolId: 'MQTT',
        protocolVersion: 5,
        clean: true,
        connectTimeout: 5000,
        username: process.env.EMQX_USERNAME,
        password: process.env.EMQX_PASSWORD,
        reconnectPeriod: 1000,
      });

      let messageReceived = false;

      tempClient.on('connect', () => {
        // Subscribe to the topic to receive the retained message
        tempClient.subscribe(topic, { qos: 1 }, (error) => {
          if (error) {
            this.logger.error('Error subscribing to topic:' + JSON.stringify(error));
            tempClient.end();
            reject(error);
          }
        });
      });

      tempClient.on('message', (receivedTopic, message) => {
        if (receivedTopic === topic) {
          messageReceived = true;
          tempClient.end();
          resolve(message.toString());
        }
      });

      tempClient.on('error', (error) => {
        tempClient.end();
        this.logger.error('Error in temporary client:' + JSON.stringify(error));
        reject(error);
      });
    });
  }

  public subscribe(topic: string, callback: (message: string, receivedTopic: string) => void): void {
    this.mqtt.subscribe(topic, { qos: 1 });
    this.mqtt.on('message', (receivedTopic, message) => {
      // Check if the received topic matches the subscription pattern
      if (this.topicMatches(topic, receivedTopic)) {
        callback(message.toString(), receivedTopic);
      }
    });
  }

  private topicMatches(subscriptionTopic: string, receivedTopic: string): boolean {
    // Convert MQTT wildcard pattern to regex
    // # matches any number of levels
    // + matches exactly one level
    const regexPattern = subscriptionTopic
      .replace(/\+/g, '[^/]+')  // + matches one level (anything except /)
      .replace(/#/g, '.*');     // # matches any number of levels (anything)

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(receivedTopic);
  }
}
