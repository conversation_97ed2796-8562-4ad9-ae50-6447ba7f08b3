import { IApplicationService, Result, IdGenerator, EncryptorBcrypt } from '@core/core';

import { SignUpEntryDto } from '../dto/entry/sign-up-entry.application.dto';
import { SignUpResponseDto } from '../dto/response/sign-up-response.application.dto';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { User } from '@modules/modules/user/domain/user';
import { emailTakenException } from '../exceptions/email-taken.exception';
export class SignUpUserApplicationService implements IApplicationService<SignUpEntryDto, SignUpResponseDto> {
  private readonly userRepository: IUserRepository;
  private readonly uuidGenerator: IdGenerator<string>;

  constructor(userRepository: IUserRepository, uuidGenerator: IdGenerator<string>) {
    this.userRepository = userRepository;
    this.uuidGenerator = uuidGenerator;
  }

  async execute(signUpDto: SignUpEntryDto): Promise<Result<SignUpResponseDto>> {
    const findResult = await this.userRepository.findUserByEmail(signUpDto.email);

    if (findResult.isPresent()) return Result.fail(emailTakenException());

    const idUser = await this.uuidGenerator.generateId();

    const encryptor = new EncryptorBcrypt();
    const password = await encryptor.hashPassword(signUpDto.password);

    const create = User.create(idUser, signUpDto.name, signUpDto.email, password);
    await this.userRepository.saveUser(create);

    const answer = {
      id: idUser,
      email: signUpDto.email,
      name: signUpDto.name,
      password: signUpDto.password,
    };
    return Result.success(answer);
  }

  get name(): string {
    return this.constructor.name;
  }
}
