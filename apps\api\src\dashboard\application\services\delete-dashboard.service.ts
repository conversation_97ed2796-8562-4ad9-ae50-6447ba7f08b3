import { IApplicationService, Result } from '@core/core';
import { IDashboardRepository } from '@modules/modules/dashboard/domain/repository/dashboard-repository.interface';
import { DeleteDashboardEntryDto } from '../dto/entry/delete-dashboard.application.dto';
import { DeleteDashboardResponseDto } from '../dto/response/delete-dashboard-response.application.dto';

export class DeleteDashboardApplicationService
  implements IApplicationService<DeleteDashboardEntryDto, DeleteDashboardResponseDto>
{
  constructor(private readonly dashboardsRepository: IDashboardRepository) {}

  async execute(data: DeleteDashboardEntryDto): Promise<Result<DeleteDashboardResponseDto>> {
    await this.dashboardsRepository.deleteDashboard(data.dashboardId);

    return Result.success({ response: 'dashboard eliminado con éxito' });
  }
  get name(): string {
    return this.constructor.name;
  }
}
