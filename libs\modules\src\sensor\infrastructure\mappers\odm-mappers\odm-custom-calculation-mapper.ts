
import { <PERSON><PERSON><PERSON><PERSON> } from '@core/core';
import { Model } from 'mongoose';
import { OdmCustomCalculationEntity } from '../../entities/odm-entities/odm-custom-calculation.entity';
import { CustomCalculation } from '@modules/modules/sensor/domain/custom-calculation';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class OdmCustomCalculationMapper implements IMapper<CustomCalculation, OdmCustomCalculationEntity> {
  constructor(
    @InjectModel(OdmCustomCalculationEntity.name)
    private readonly customCalculationModel: Model<OdmCustomCalculationEntity>,
  ) {}

  fromDomainToPersistence(domain: CustomCalculation): Promise<OdmCustomCalculationEntity> {
    return new Promise(async (resolve, reject) => {
      const customCalculation = new this.customCalculationModel({
        _id: domain.Id,
        sensorId: domain.SensorId,
        calculations: domain.Calculations,
      });
      resolve(customCalculation);
    });
  }
  async fromPersistenceToDomain(customCalculation: OdmCustomCalculationEntity): Promise<CustomCalculation> {
    return CustomCalculation.create(customCalculation._id, customCalculation.sensorId, customCalculation.calculations);
  }
}
