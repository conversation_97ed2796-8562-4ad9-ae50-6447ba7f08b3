import * as bcrypt from 'bcryptjs';
import { IEncryptor } from '../../application/encryptor/encryptor.interface';

export class EncryptorBcrypt implements IEncryptor {
  constructor() {}
  async hashPassword(planePassword: string): Promise<string> {
    return bcrypt.hash(planePassword, 10);
  }
  async comparePlaneAndHash(planePassword: string, hashPassword: string): Promise<boolean> {
    return bcrypt.compare(planePassword, hashPassword);
  }
}
