import { IApplicationService, Result } from '@core/core';

import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { addActuatorUserEntryDto } from '../dto/entry/add-actuator-user.application.dto';
import { addActuatorUserResponseDto } from '../dto/response/add-actuator-user-response.application.dto';
import { userNotFoundException } from '../exceptions/not-found';
import { actuatorNameInUseException } from '../exceptions/actuator-name-in-use';

export class AddActuatorUserApplicationService
  implements IApplicationService<addActuatorUserEntryDto, addActuatorUserResponseDto>
{
  private readonly userRepository: IUserRepository;

  constructor(userRepository: IUserRepository) {
    this.userRepository = userRepository;
  }

  async execute(addActuatorDto: addActuatorUserEntryDto): Promise<Result<addActuatorUserResponseDto>> {
    const user = await this.userRepository.findUserById(addActuatorDto.userId);
    if (!user.isPresent()) return Result.fail(userNotFoundException());

    const userFound = user.get();

    const userHasActuator = await this.userRepository.userHasActuatorByName(userFound.Id, addActuatorDto.actuator.Name);
    if (userHasActuator) return Result.fail(actuatorNameInUseException());

    await this.userRepository.addActuatorToUser(addActuatorDto.userId, addActuatorDto.actuator);

    return Result.success<addActuatorUserResponseDto>({ id: addActuatorDto.actuator.Id });
  }

  get name(): string {
    return this.constructor.name;
  }
}
