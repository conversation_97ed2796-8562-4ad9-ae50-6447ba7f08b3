import { Entity } from '@core/core';
import { Sensor } from '@modules/modules/sensor/domain/sensor';

export class Measurement extends Entity<string> {
  private creationDate: Date;
  private measure: number;
  private sensorId: string;
  private userId: string;

  private constructor(id: string, creationDate: Date, measure: number, sensorId: string, userId: string) {
    super(id);
    this.creationDate = creationDate;
    this.measure = measure;
    this.sensorId = sensorId;
    this.userId = userId;
  }

  get CreationDate(): Date {
    return this.creationDate;
  }

  get Measure(): number {
    return this.measure;
  }

  get SensorId(): string {
    return this.sensorId;
  }

  get UserId(): string {
    return this.userId;
  }

  static create(id: string, creationDate: Date, measure: number, sensorId: string, userId: string): Measurement {
    return new Measurement(id, creationDate, measure, sensorId, userId);
  }
}
