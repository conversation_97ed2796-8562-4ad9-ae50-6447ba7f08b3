import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ collection: 'actuator_tokens' })
export class OdmActuatorTokenEntity {
  readonly _id: string;

  @Prop({ required: true, type: String, index: true })
  token: string;

  @Prop({ type: Date, default: Date.now })
  creationDate: Date;

  @Prop({ required: true, type: String })
  actuatorId: string;

  @Prop({ required: true, type: String })
  userId: string;
}

export const ActuatorTokenSchema = SchemaFactory.createForClass(OdmActuatorTokenEntity);
