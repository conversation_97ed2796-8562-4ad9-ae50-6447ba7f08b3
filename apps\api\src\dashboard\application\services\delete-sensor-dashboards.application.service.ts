
import { IApplicationService, Result } from '@core/core';
import { IDashboardRepository } from '@modules/modules/dashboard/domain/repository/dashboard-repository.interface';
import { DeleteSensorDashboardsEntryDto } from '../dto/entry/delete-sensor-dashboards.application.dto'
import { DeleteSensorDashboardsResponseDto } from '../dto/response/delete-sensor-dashboards-response.application.dto'

export class DeleteSensorDashboardsApplicationService
  implements IApplicationService<DeleteSensorDashboardsEntryDto, DeleteSensorDashboardsResponseDto>
{
  constructor(private readonly dashboardsRepository: IDashboardRepository) {}

  async execute(data: DeleteSensorDashboardsEntryDto): Promise<Result<DeleteSensorDashboardsResponseDto>> {
    await this.dashboardsRepository.deleteDashboardsBySensorId(data.sensorId);

    return Result.success({ response: 'dashboards eliminados con éxito' });
  }
  get name(): string {
    return this.constructor.name;
  }
}
