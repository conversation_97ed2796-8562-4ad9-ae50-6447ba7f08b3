import { Module } from '@nestjs/common';
import { SensorGateway } from './sensor/infrastructure/gateways/sensor.gateway';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { SensorEntityModule } from '@modules/modules/sensor/infrastructure/sensor-entity.module';
import { SensorConnectionService } from './sensor/infrastructure/services/sensor-connection.service';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { CoreModule } from '@core/core/core.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UserConnectionService } from './sensor/infrastructure/services/user-connection.service';
import { MeasurementGateway } from './sensor/infrastructure/gateways/measurement.gateway';
import { JwtModule } from '@nestjs/jwt';
import { ActuatorGateway } from './actuator/infrastructure/gateways/actuator.gateway'

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET_KEY,
      signOptions: { expiresIn: '48h' },
    }),
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    UserEntityModule,
    CoreModule,
    ConfigModule.forRoot({ isGlobal: true, envFilePath: '.env' }),
    MongooseModule.forRoot(process.env.MONGO_DB!, { dbName: 'tank-monitor-db' }),
    SensorEntityModule,
  ],
  providers: [SensorGateway, MeasurementGateway, ActuatorGateway,SensorConnectionService, UserConnectionService],
})
export class WsModule {}
