import { Entity } from '@core/core';
import { ICalculation } from './types/calculation.type'

export class CustomCalculation extends Entity<string> {
    private sensorId: string;
    private calculations: ICalculation[];

    private constructor(id: string, sensorId: string, calculations: ICalculation[]) {
        super(id);
        this.sensorId = sensorId;
        this.calculations = calculations;
    }

    get SensorId(): string {
        return this.sensorId;
    }

    get Calculations(): ICalculation[] {
        return this.calculations;
    }

    static create(id: string, sensorId: string, calculations: ICalculation[]): CustomCalculation {
        return new CustomCalculation(id, sensorId, calculations);
    }
}
