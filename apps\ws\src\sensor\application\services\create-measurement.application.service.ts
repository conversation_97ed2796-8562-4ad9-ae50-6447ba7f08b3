import { IApplicationService, Result, IdGenerator } from '@core/core';
import { CreateMeasurementEntryDto } from '../dto/entry/create-measurement.application.dto';
import { Measurement } from '@modules/modules/sensor/domain/measurement';
import { IMeasurementRepository } from '@modules/modules/sensor/domain/repository/measurement-repository.interface';
import { CreateMeasurementResponseDto } from '../dto/response/create-measurement-response.application.dto';
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface';
import { sensorNotFoundError } from '../exceptions/not-found';
import { IEventEmitter } from '@core/core/application/event-emitter/event-emitter.interface';
import { MeasurementCreatedEvent } from '../events/measurement-created.event';

export class CreateMeasurementApplicationService
  implements IApplicationService<CreateMeasurementEntryDto, CreateMeasurementResponseDto>
{
  constructor(
    private readonly uuidGenerator: IdGenerator<string>,
    private readonly measurementRepository: IMeasurementRepository,
    private readonly sensorRepository: ISensorRepository,
    private readonly eventEmitter: IEventEmitter,
  ) {}

  async execute(createMeasurementDto: CreateMeasurementEntryDto): Promise<Result<CreateMeasurementResponseDto>> {
    const sensor = await this.sensorRepository.findSensor(createMeasurementDto.userId, createMeasurementDto.sensorId);

    if (!sensor.isPresent()) {
      return Result.fail(sensorNotFoundError());
    }

    const idMeasurement = await this.uuidGenerator.generateId();

    const currentDate = new Date();

    const measurement: Measurement = Measurement.create(
      idMeasurement,
      currentDate,
      createMeasurementDto.measure,
      createMeasurementDto.sensorId,
      createMeasurementDto.userId,
    );
    await this.measurementRepository.save(measurement);

    const measurementCreatedEvent: MeasurementCreatedEvent = {
      userId: createMeasurementDto.userId,
      sensorId: createMeasurementDto.sensorId,
      measurement: createMeasurementDto.measure,
      date: currentDate,
    };

    const results = await this.eventEmitter.emitAsync('measurement.created', measurementCreatedEvent);

    for (const result of results) {
      if (!result.isSuccess()) {
        return Result.fail(result.Error);
      }
    }

    return Result.success({ id: idMeasurement });
  }

  get name(): string {
    return this.constructor.name;
  }
}
