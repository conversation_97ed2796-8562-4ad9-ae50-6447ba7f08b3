import { Controller, Get, Param, UseGuards } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger"
import { Observable } from 'rxjs'
import { EmqxApiService } from '../services/emqx-api.service'
import { EmqxClientsResponseDto } from '../dto/emqx-client.dto'
import { JwtAuthGuard } from '@core/core'

@ApiTags('Common')
@Controller('common')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CommonController {

    constructor(
        private readonly emqxApiService: EmqxApiService,
    ) {
    }

    @Get('emqx/clients/active')
    @ApiOperation({ summary: 'Get active EMQX clients' })
    @ApiResponse({
        status: 200,
        description: 'List of active EMQX clients',
        type: EmqxClientsResponseDto
    })
    @ApiResponse({ status: 401, description: 'Unauthorized' })
    getActiveClients(): Observable<EmqxClientsResponseDto | null> {
        return this.emqxApiService.getActiveClients();
    }

}