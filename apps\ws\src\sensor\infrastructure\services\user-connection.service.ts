import { User } from '@modules/modules/user/domain/user';
import { OdmUserRepository } from '@modules/modules/user/infrastructure/repositories/odm-repository/odm-user-repository';
import { Injectable } from '@nestjs/common';
import { Socket } from 'socket.io';

interface UserConnection {
  socket: Socket;
  user: User;
  suscribedSensors: string[];
}

interface ConnectedUsers {
  [clientId: string]: UserConnection;
}

interface UserToSocketClient {
  [userId: string]: { socket: Socket };
}

@Injectable()
export class UserConnectionService {
  constructor(private readonly userRepository: OdmUserRepository) {}

  private connectedUsers: ConnectedUsers = {};
  private userToSocketClient: UserToSocketClient = {};

  async register(client: Socket, userId: string): Promise<void> {
    const user = await this.userRepository.findUserById(userId);

    if (!user.isPresent()) {
      throw new Error('User not found');
    }

    // this.checkUserConnection(userId);

    this.connectedUsers[client.id] = {
      socket: client,
      user: user.get(),
      suscribedSensors: [],
    };

    this.userToSocketClient[userId] = { socket: client };
  }

  removeUser(clientId: string) {
    const userConnection = this.connectedUsers[clientId];
    delete this.connectedUsers[clientId];
    if (!userConnection || !userConnection.user) {
      return;
    }
    delete this.userToSocketClient[userConnection.user.Id];
  }

  subscribeSensor(clientId: string, sensorId: string) {
    const userConnection = this.connectedUsers[clientId];

    if (!userConnection) {
      return;
    }

    const userOwnsSensor = !!userConnection?.user.Sensors?.find((s) => s.Id === sensorId);
    const isUserNotSubscribed = !userConnection.suscribedSensors.find((s) => s === sensorId);

    if (userOwnsSensor && isUserNotSubscribed) {
      userConnection.suscribedSensors.push(sensorId);
    }
  }

  getUserFromClientId(clientId: string) {
    return this.connectedUsers[clientId];
  }

  unsubscribeSensor(clientId: string, sensorId: string) {
    const userConnection = this.connectedUsers[clientId];

    if (!userConnection) {
      return;
    }

    userConnection.suscribedSensors = userConnection.suscribedSensors.filter((s) => s !== sensorId);
  }

  notifyMeasurement(userId: string, sensorId: string, measurement: number) {
    const userClient = this.userToSocketClient[userId];

    if (!userClient) {
      return;
    }

    const userConnection = this.connectedUsers[userClient.socket.id];

    const isUserSubscribed = userConnection.suscribedSensors.find((s) => s === sensorId);

    if (isUserSubscribed) {
      userClient.socket.emit('measurement', { sensorId, measurement });
    }
  }

  // notifySensorConnection(userId: string, sensorId: string) {
  //   const userConnection = this.userToSocketClient[userId];
  //   if (userConnection) {
  //     userConnection.socket.emit('sensor-connection', {
  //       sensorId,
  //     });
  //   }
  // }

  // notifySensorDisconnection(userId: string, sensorId: string) {
  //   const userConnection = this.userToSocketClient[userId];
  //   if (userConnection) {
  //     userConnection.socket.emit('sensor-disconnection', {
  //       sensorId,
  //     });
  //   }
  // }

  // private checkUserConnection(userId: string) {
  //   for (const clientId of Object.keys(this.connectedUsers)) {
  //     const connectedSensor = this.connectedUsers[clientId];

  //     if (connectedSensor.user.Id === userId) {
  //       connectedSensor.socket.disconnect();
  //       break;
  //     }
  //   }
  // }
}
