import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ComparisonTypesEnum } from '../../../domain/enum/comparison-types.enum';
import { IActuatorAction } from '@modules/modules/trigger/domain/types/actuator-action.type'

@Schema({ _id: false, collection: 'trigger' })
export class OdmTriggerEntity {
  @Prop({ required: true, type: String })
  _id: string;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ required: false, type: String })
  sensorId: string;

  @Prop({ required: true, type: String })
  userId: string;

  @Prop({ required: true, type: [{ actuatorId: String, action: String }] })
  actuators: IActuatorAction[];

  @Prop({ required: true, type: String, enum: ComparisonTypesEnum })
  comparison: ComparisonTypesEnum;

  @Prop({ required: false, type: [Number] })
  objectiveMeasures: number[];

  @Prop({ required: false, type: String })
  objectiveHour: string;

  @Prop({ type: Date, default: Date.now })
  creationDate: Date;
}

export const TriggerSchema = SchemaFactory.createForClass(OdmTriggerEntity);
