import { IDashboardRepository } from '@modules/modules/dashboard/domain/repository/dashboard-repository.interface';
import { Injectable } from '@nestjs/common';
import { OdmDashboardMapper } from '../../mappers/odm-mappers/odm-dashboard-mapper';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Dashboard } from '@modules/modules/dashboard/domain/dashboard';
import { Optional } from '@core/core/domain/optional/optional';
import { PaginationInfo } from '@core/core/infrastructure/pagination/interfaces/pagination.info'
import { makePaginationInfo } from '@core/core/infrastructure/pagination/helpers/pagination.factory'
import { OdmDashboardEntity } from '../../entities/odm-entities/odm-dashboard.entity'

@Injectable()
export class OdmDashboardRepository implements IDashboardRepository {
  constructor(
    @InjectModel(OdmDashboardEntity.name)
    private readonly DashboardModel: Model<OdmDashboardEntity>,
    private readonly dashboardMapper: OdmDashboardMapper,
  ) {}


  async deleteDashboard ( dashboardId: string ): Promise<void>
  {
    await this.DashboardModel.deleteOne({ _id: dashboardId })
  }


  async saveDashboard ( dashboard: Dashboard ): Promise<void>
  {
    const dashboardOdm = await this.dashboardMapper.fromDomainToPersistence(dashboard);
    const newDashboardOdm = new this.DashboardModel(dashboardOdm);
    await newDashboardOdm.save();
  }

  async updateDashboardPosition(id: string, position: number): Promise<void>{
    await this.DashboardModel.updateOne({_id: id}, {
      position: position
    })
  }
  
  async findDashboardById(dashboardId: string): Promise<Optional<Dashboard>> {
    const dashboard = await this.DashboardModel.findById(dashboardId).exec();
    if (!dashboard) {
      return Optional.of<Dashboard>(null);
    }
    return Optional.of(await this.dashboardMapper.fromPersistenceToDomain(dashboard));
  }
  
  async findDashboardsByUserId(userId: string, page: number, limit: number): Promise<Dashboard[]> {
    const dashboards = await this.DashboardModel.find({userId: userId}).sort({position: 1}).exec();
    if (!dashboards) {
      return [];
    }
    const domainDashboards: Dashboard[] = [];
    dashboards.forEach(async (dashboard) =>
      domainDashboards.push(await this.dashboardMapper.fromPersistenceToDomain(dashboard)),
    );
    return domainDashboards;
  }

  async findManyPagination ( userId: string, page: number, limit: number ): Promise<PaginationInfo>
  {
    return this.DashboardModel.aggregate([
      { $match: { userId: userId } },
      { $count: 'total' },
    ]).then((result) => {
      const total: number = result[0]?.total || 0;
      return makePaginationInfo({ count: total, page, limit })
    });
  }
}
