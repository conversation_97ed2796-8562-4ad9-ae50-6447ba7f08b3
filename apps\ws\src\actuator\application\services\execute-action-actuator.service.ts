
import { IApplicationService, Result } from '@core/core';
import { IActuatorRepository } from '@modules/modules/actuator/domain/repository/actuator-repository.interface'
import { ExecuteActionActuatorEntryDto } from '../dto/entry/execute-action-actuator.application.dto'
import { actuatorNotFoundException } from '../exceptions/not-found'
import { IMessageSender } from '@core/core/application/messages-sender/message-sender.interface'
import { MqttTopics } from '@core/core/application/messages-sender/enum/mqtt-topics.enum'
import { ExecuteActionActuatorResponseDto } from '../dto/response/execute-action-actuator-response.application.dto'

export class ExecuteActionActuatorService
  implements IApplicationService<ExecuteActionActuatorEntryDto, ExecuteActionActuatorResponseDto>
{
  constructor(
    private readonly actuatorsRepository: IActuatorRepository,
    private readonly messageSender: IMessageSender,
  ) {}

  async execute(data: ExecuteActionActuatorEntryDto): Promise<Result<ExecuteActionActuatorResponseDto>> {
    const actuators = await this.actuatorsRepository.findActuatorById(data.userId, data.actuatorId);
    if (!actuators.isPresent()) {
      console.error(`Actuator with ID ${data.actuatorId} not found for user ${data.userId}`);
      return Result.fail(actuatorNotFoundException());
    }
    this.messageSender.emit(MqttTopics.ACTUATOR_ACTION + '/' + data.actuatorId, JSON.stringify({
      action: data.action,
      actuatorId: data.actuatorId,
    }), false);

    return Result.success({
        success: true,
    });
  }
  get name(): string {
    return this.constructor.name;
  }
}
