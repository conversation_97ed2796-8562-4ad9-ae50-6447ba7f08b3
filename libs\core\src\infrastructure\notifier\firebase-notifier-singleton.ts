import { IPushSender } from '@core/core/application';
import { PushMulticastDto, PushNotificationDto } from '@core/core/application/push-sender/dto/token-notification.dto';
import { Result } from '@core/core/domain';
import * as admin from 'firebase-admin';
import { pushErrorException } from './exceptions/push-error.exception';
import { Message } from 'firebase-admin/lib/messaging';

export class FirebaseNotifier implements IPushSender {
  private static instance: FirebaseNotifier;

  private constructor() {
    const credentials: object = {
      type: process.env.FB_TYPE,
      project_id: process.env.FB_PROJECT_ID,
      private_key_id: process.env.FB_PRIVATE_KEY_ID,
      private_key: process.env.FB_PRIVATE_KEY!.replace(/\\n/gm, '\n'),
      client_email: process.env.FB_CLIENT_EMAIL,
      client_id: process.env.FB_CLIENT_ID,
      auth_uri: process.env.FB_AUTH_URI,
      token_uri: process.env.FB_TOKEN_URI,
      auth_provider_x509_cert_url: process.env.FB_AUTH_PROVIDER,
      client_x509_cert_url: process.env.FB_CLIENT,
      universe_domain: process.env.FB_DOMAIN,
    };
    admin.initializeApp({ credential: admin.credential.cert(credentials) });
  }

  async sendMulticastPush(message: PushMulticastDto): Promise<void> {
    const icon =
      'https://firebasestorage.googleapis.com/v0/b/tank-monitor-a5104.firebasestorage.app/o/water-level-256x256.png?alt=media&token=b73b5958-e473-46f4-91bb-cb2c8ab6f2c4';
    const msg = {
      tokens: message.token,
      android: { notification: { title: message.notification.title, body: message.notification.body, icon: icon } },
      webpush: { notification: { title: message.notification.title, body: message.notification.body, icon: icon } },
    };
    const res = await admin.messaging().sendEachForMulticast(msg);
  }

  public static getInstance(): FirebaseNotifier {
    if (!FirebaseNotifier.instance) FirebaseNotifier.instance = new FirebaseNotifier();
    return FirebaseNotifier.instance;
  }

  async sendNotificationPush(message: PushNotificationDto): Promise<Result<string>> {
    const icon =
      'https://firebasestorage.googleapis.com/v0/b/tank-monitor-a5104.firebasestorage.app/o/water-level-256x256.png?alt=media&token=b73b5958-e473-46f4-91bb-cb2c8ab6f2c4';
    const msg: Message = {
      token: message.token,
      android: {
        notification: {
          title: message.notification.title,
          body: message.notification.body,
          icon: icon,
          clickAction: '/',
        },
      },
      webpush: {
        fcmOptions: { link: process.env.FRONTEND_URL! + message.customRedirect ? message.customRedirect : '' },
        notification: {
          title: message.notification.title,
          body: message.notification.body,
          icon: icon,
          badge: icon,
          //actions: [ { action: 'idk', title: 'Eso Brad', icon: icon } ],
        },
      },
      data: {
        eventType: message.eventType,
        alertId: 'XYZ-789',
      },
    };
    try {
      const res = await admin
        .messaging()
        .send(msg)
        .then((e) => {
          console.log(' sended ');
        });
      return Result.success<string>('push_sended');
    } catch (e) {
      return Result.fail<string>(pushErrorException());
    }
  }
}
