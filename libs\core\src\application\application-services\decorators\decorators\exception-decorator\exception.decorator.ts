import { Result } from '../../../../../domain/result-handler/Result';
import { ApplicationServiceDecorator } from '../../application-service.decorator';
import { ApplicationServiceEntryDto } from '../../../dto/application-service-entry.dto';
import { IApplicationService } from '../../../application-service.interface';
import { IExceptionHandler } from '../../../../exception-handler/exception-handler.interface';
import { Exception } from '../../../../../domain/exception/exception';

export class ExceptionDecorator<D extends ApplicationServiceEntryDto, R>
  extends ApplicationServiceDecorator<D, R>
  implements IApplicationService<D, R>
{
  private readonly exceptionHandler: IExceptionHandler;

  constructor(applicationService: ApplicationServiceDecorator<D, R>, exceptionHandler: IExceptionHandler) {
    super(applicationService);
    this.exceptionHandler = exceptionHandler;
  }

  async execute(data: D): Promise<Result<R>> {
    let result: Result<R>;
    try {
      result = await this.applicationService.execute(data);
      if (result.isSuccess()) return result;
    } catch (error) {
      const exception: Exception = {
        code: 'SE-001',
        message: 'An exception occurred in the execution of the application service',
        info: 'An exception occurred, check the server logs for more information',
      };
      this.exceptionHandler.HandleException(exception);
      throw error; // for typing
    }
    this.exceptionHandler.HandleException(result.Error);
    throw result.Error; // for typing
  }
}
