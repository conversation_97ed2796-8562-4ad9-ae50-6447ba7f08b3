import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Observable, catchError, map, of } from 'rxjs';
import { AxiosResponse } from 'axios';
import { EmqxClientsResponseDto } from '../dto/emqx-client.dto';

@Injectable()
export class EmqxApiService {
  private readonly logger = new Logger(EmqxApiService.name);
  private readonly emqxBaseUrl: string;
  private readonly emqxUsername: string;
  private readonly emqxPassword: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    
    
    // EMQX management API typically runs on port 18083
    this.emqxBaseUrl = configService.get<string>('EMQX_FETCH_URL', 'http://localhost:18083');
    this.emqxUsername = this.configService.get<string>('EMQX_FETCH_USERNAME', 'admin');
    this.emqxPassword = this.configService.get<string>('EMQX_FETCH_PASSWORD', 'public');
  }

  getActiveClients(): Observable<EmqxClientsResponseDto | null> {
    // Create base64 encoded credentials for Basic Auth
    const credentials = Buffer.from(`${this.emqxUsername}:${this.emqxPassword}`).toString('base64');

    const headers = {
      'Authorization': `Basic ${credentials}`,
      'Content-Type': 'application/json',
    };

    const params = {
      connected: 'true',
    };

    return this.httpService
      .get<EmqxClientsResponseDto>(`${this.emqxBaseUrl}/api/v5/clients`, {
        headers,
        params,
      })
      .pipe(
        map((response: AxiosResponse<EmqxClientsResponseDto>) => response.data),
        catchError((error) => {
          this.logger.error('Error fetching EMQX clients:', error.message);
          if (error.response) {
            this.logger.error('Response status:', error.response.status);
            this.logger.error('Response data:', error.response.data);
          }
          return of(null);
        }),
      );
  }

}
