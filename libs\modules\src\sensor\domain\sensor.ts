import { Entity } from '@core/core';
import { SensorTypesEnum } from './enums/sensor-types.enum'

export class Sensor extends Entity<string> {
  private name: string;
  private type: SensorTypesEnum;

  private constructor(id: string, name: string, type: SensorTypesEnum) {
    super(id);
    this.name = name;
    this.type = type;
  }

  get Name(): string {
    return this.name;
  }

  get Type(): SensorTypesEnum {
    return this.type;
  }

  static create(id: string, name: string, type: SensorTypesEnum): Sensor {
    return new Sensor(id, name, type);
  }
}
