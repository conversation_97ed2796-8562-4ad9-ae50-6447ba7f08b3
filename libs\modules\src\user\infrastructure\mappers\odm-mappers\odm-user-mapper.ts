import { OdmUserEntity } from '../../entities/odm-entities/odm-user.entity';
import { I<PERSON>apper } from '@core/core';
import { Actuator } from '@modules/modules/actuator/domain/actuator'
import { Sensor } from '@modules/modules/sensor/domain/sensor';
import { Trigger } from '@modules/modules/trigger/domain/trigger'
import { OdmTriggerEntity } from '@modules/modules/trigger/infrastructure/entities/odm-entities/odm-trigger.entity'
import { User } from '@modules/modules/user/domain/user';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class OdmUserMapper implements IMapper<User, OdmUserEntity> {
  constructor(
    @InjectModel(OdmUserEntity.name)
    private readonly userModel: Model<OdmUserEntity>,
  ) {}

  fromDomainToPersistence(domain: User): Promise<OdmUserEntity> {
    return new Promise(async (resolve, reject) => {
      const user = new this.userModel({
        _id: domain.Id,
        name: domain.Name,
        email: domain.Email,
        password: domain.Password,
        sensors: domain.Sensors?.map((sensor) => ({
          _id: sensor.Id,
          name: sensor.Name,
          type: sensor.Type,
        })),
        actuators: domain.Actuators?.map((actuator) => ({
          _id: actuator.Id,
          name: actuator.Name,
          type: actuator.Type,
        })),
      });
      resolve(user);
    });
  }
  async fromPersistenceToDomain(user: OdmUserEntity, triggers?: OdmTriggerEntity[]): Promise<User> {
    return User.create(
      user._id,
      user.name,
      user.email,
      user.password,
      user.sensors.map((sensor) => Sensor.create(sensor._id, sensor.name, sensor.type)),
      user.actuators.map((actuator) => Actuator.create(actuator._id, actuator.name, actuator.type)),
      triggers?.map((trigger) => Trigger.create(trigger._id, trigger.name, trigger.creationDate, trigger.userId, trigger.comparison, trigger.actuators, trigger.objectiveMeasures, trigger.sensorId, trigger.objectiveHour)),
    );
  }
}
