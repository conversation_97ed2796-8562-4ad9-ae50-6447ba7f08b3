import { TriggerEntityModule } from '@modules/modules/trigger/infrastructure/trigger-entity.module';
import { Module } from '@nestjs/common';
import { Trigger<PERSON>ontroller } from './controller/trigger.controller';
import { AuthModule } from '../../auth/infrastructure/auth.module';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { CoreModule } from '@core/core/core.module';
import { ActuatorEntityModule } from '@modules/modules/actuator/infrastructure/actuator-entity.module';
import { SensorEntityModule } from '@modules/modules/sensor/infrastructure/sensor-entity.module';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    ActuatorEntityModule,
    TriggerEntityModule,
    SensorEntityModule,
    AuthModule,
    UserEntityModule,
    CoreModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [TriggerController],
})
export class TriggerModule {}
