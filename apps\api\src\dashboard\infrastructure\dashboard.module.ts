import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/infrastructure/auth.module';
import { UserEntityModule } from '@modules/modules/user/infrastructure/user-entity.module';
import { CoreModule } from '@core/core/core.module';
import { DashboardController } from './controller/dashboard.controller'
import { DashboardEntityModule } from '@modules/modules/dashboard/infrastructure/dashboard-entity.module'
import { SensorEntityModule } from '@modules/modules/sensor/infrastructure/sensor-entity.module'

@Module({
  imports: [DashboardEntityModule, AuthModule, UserEntityModule, SensorEntityModule],
  controllers: [DashboardController],
})
export class DashboardModule {}
