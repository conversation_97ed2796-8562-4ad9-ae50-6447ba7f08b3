import { Entity } from '@core/core';
import { ComparisonTypesEnum } from './enum/comparison-types.enum';
import { IActuatorAction } from './types/actuator-action.type';

export class Trigger extends Entity<string> {

  private name: string;
  private creationDate: Date;
  private objectiveMeasure?: number[];
  private sensorId?: string;
  private userId: string;
  private comparison: ComparisonTypesEnum;
  private actuators: IActuatorAction[];
  private objectiveHour?: string;

  private constructor(
    id: string,
    name: string,
    creationDate: Date,
    userId: string,
    comparison: ComparisonTypesEnum,
    actuators: IActuatorAction[],
    sensorId?: string,
    objectiveMeasure?: number[],
    objectiveHour?: string,
  ) {
    super(id);
    this.name = name;
    this.creationDate = creationDate;
    this.objectiveMeasure = objectiveMeasure;
    this.sensorId = sensorId;
    this.userId = userId;
    this.comparison = comparison;
    this.actuators = actuators;
    this.objectiveHour = objectiveHour;
  }

  get Name(): string {
    return this.name;
  }

  get CreationDate(): Date {
    return this.creationDate;
  }

  get ObjectiveMeasure(): number[] | undefined {
    return this.objectiveMeasure;
  }

  get SensorId(): string | undefined {
    return this.sensorId;
  }

  get UserId(): string {
    return this.userId;
  }

  get Comparison(): ComparisonTypesEnum {
    return this.comparison;
  }

  get Actuators(): IActuatorAction[] {
    return this.actuators;
  }

  get ObjectiveHour(): string | undefined {
    return this.objectiveHour;
  }

  private validate(): void {

    if ((this.objectiveMeasure && this.objectiveMeasure.length > 0) || this.sensorId) {
      if (this.comparison == ComparisonTypesEnum.in && (!this.objectiveMeasure || this.objectiveMeasure?.length != 2)) {
        throw new Error('Objective measure must be defined and have two values for comparison "in"');
      }

      if (this.comparison != ComparisonTypesEnum.in && (!this.objectiveMeasure || this.objectiveMeasure?.length != 1)) {
        throw new Error('Objective measure must be defined and have one value for comparison not range');
      }
    }

    if (this.objectiveHour && this.SensorId) {
      throw new Error('Objective hour and sensor id cannot be defined at the same time');
    }

    if (this.objectiveHour && (this.objectiveMeasure && this.objectiveMeasure.length > 0)) {
      throw new Error('Objective hour and objective measure cannot be defined at the same time');
    }

    if (
      this.objectiveHour &&
      (parseInt(this.objectiveHour.split(' ')[1]) > 23 || parseInt(this.objectiveHour.split(' ')[1]) < 0)
    ) {
      throw new Error('Objective hour must be between 0 and 23');
    }

    if (
      this.objectiveHour &&
      (parseInt(this.objectiveHour.split(' ')[0]) > 59 || parseInt(this.objectiveHour.split(' ')[0]) < 0)
    ) {
      throw new Error('Objective minute must be between 0 and 59');
    }

    if ((this.objectiveMeasure && this.objectiveMeasure.length > 0) && !this.sensorId) {
      throw new Error('Sensor id must be defined when objective hour is defined');
    }
  }

  update (
    name: string,
    creationDate: Date,
    userId: string,
    comparison: ComparisonTypesEnum,
    actuators: IActuatorAction[],
    objectiveMeasure?: number[],
    sensorId?: string,
    objectiveHour?: string,
  ) {
    this.name = name;
    this.creationDate = creationDate;
    this.objectiveMeasure = objectiveMeasure;
    this.sensorId = sensorId;
    this.userId = userId;
    this.comparison = comparison;
    this.actuators = actuators;
    this.objectiveHour = objectiveHour;

    this.validate();
  }

  static create(
    id: string,
    name: string,
    creationDate: Date,
    userId: string,
    comparison: ComparisonTypesEnum,
    actuators: IActuatorAction[],
    objectiveMeasure?: number[],
    sensorId?: string,
    objectiveHour?: string,
  ): Trigger {
    const trigger = new Trigger(
      id,
      name,
      creationDate,
      userId,
      comparison,
      actuators,
      sensorId,
      objectiveMeasure,
      objectiveHour,
    );

    trigger.validate();

    return trigger;
  }
}
