import { IApplicationService, Result } from '@core/core';
import { UpdateSensorNameEntryDto } from '../dto/entry/update-sensor-name.application.dto';
import { UpdateSensorNameResponseDto } from '../dto/response/update-sensor-name-response.application.dto';
import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { ISensorRepository } from '@modules/modules/sensor/domain/repository/sensor-repository.interface';
import { sensorNotFoundException } from '../exceptions/not-found';
import { sensorNameInUseException } from 'apps/api/src/user/application/exceptions/sensor-name-in-use'

export class UpdateSensorNameApplicationService
  implements IApplicationService<UpdateSensorNameEntryDto, UpdateSensorNameResponseDto>
{
  private readonly userRepository: IUserRepository;
  private readonly sensorRepository: ISensorRepository;

  constructor(userRepository: IUserRepository, sensorRepository: ISensorRepository) {
    this.userRepository = userRepository;
    this.sensorRepository = sensorRepository;
  }

  async execute(updateSensorDto: UpdateSensorNameEntryDto): Promise<Result<UpdateSensorNameResponseDto>> {
    // Validate that the sensor exists
    const sensor = await this.sensorRepository.findSensor(updateSensorDto.userId, updateSensorDto.sensorId);
    if (!sensor.isPresent()) {
      return Result.fail(sensorNotFoundException());
    }

    // Check if the new name is already in use by another sensor
    const nameInUse = await this.userRepository.userHasSensorByName(updateSensorDto.userId, updateSensorDto.name);
    if (nameInUse) {
      // Check if it's the same sensor (allow updating with the same name)
      const existingSensor = sensor.get();
      if (existingSensor.Name !== updateSensorDto.name) {
        return Result.fail(sensorNameInUseException());
      }
    }

    // Update the sensor name
    await this.userRepository.updateSensorName(
      updateSensorDto.userId,
      updateSensorDto.sensorId,
      updateSensorDto.name
    );

    return Result.success<UpdateSensorNameResponseDto>({
      success: true,
      message: 'Sensor name updated successfully'
    });
  }

  get name(): string {
    return this.constructor.name;
  }
}
