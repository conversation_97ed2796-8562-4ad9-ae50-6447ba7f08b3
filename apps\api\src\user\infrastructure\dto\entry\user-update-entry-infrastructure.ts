import { ApiProperty } from '@nestjs/swagger';
import { IsBase64, IsOptional, IsString, <PERSON><PERSON>ength, MinLength } from 'class-validator';

export class UserUpdateEntryInfraestructureDto {
  @ApiProperty({
    required: false,
    example: '<EMAIL>',
  })
  @IsString()
  @IsOptional()
  @MinLength(8)
  email?: string;

  @ApiProperty({
    required: false,
    example: '<PERSON> Ruscio',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    required: false,
    example: 'password',
  })
  @IsString()
  @IsOptional()
  @MinLength(7)
  password?: string;

  @ApiProperty({
    required: false,
    example: '04120145758',
  })
  @IsString()
  @IsOptional()
  @MinLength(11)
  @MaxLength(11)
  phone?: string;

  @ApiProperty({
    required: false,
    example: 'base64 string',
  })
  @IsOptional()
  @IsBase64()
  image?: string;
}
