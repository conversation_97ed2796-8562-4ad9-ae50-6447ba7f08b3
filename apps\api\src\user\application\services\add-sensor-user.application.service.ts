import { IApplicationService, Result } from '@core/core';

import { IUserRepository } from '@modules/modules/user/domain/repository/user-repository.interface';
import { addSensorUserEntryDto } from '../dto/entry/add-sensor-user.application.dto';
import { addSensorUserResponseDto } from '../dto/response/add-sensor-user-response.application.dto';
import { userNotFoundException } from '../exceptions/not-found';
import { sensorNameInUseException } from '../exceptions/sensor-name-in-use';

export class AddSensorUserApplicationService
  implements IApplicationService<addSensorUserEntryDto, addSensorUserResponseDto>
{
  private readonly userRepository: IUserRepository;

  constructor(userRepository: IUserRepository) {
    this.userRepository = userRepository;
  }

  async execute(addSensorDto: addSensorUserEntryDto): Promise<Result<addSensorUserResponseDto>> {
    const user = await this.userRepository.findUserById(addSensorDto.userId);
    if (!user.isPresent()) return Result.fail(userNotFoundException());

    const userFound = user.get();

    const userHasSensor = await this.userRepository.userHasSensorByName(userFound.Id, addSensorDto.sensor.Name);
    if (userHasSensor) return Result.fail(sensorNameInUseException());

    await this.userRepository.addSensorToUser(addSensorDto.userId, addSensorDto.sensor);

    return Result.success<addSensorUserResponseDto>({ id: addSensorDto.sensor.Id });
  }

  get name(): string {
    return this.constructor.name;
  }
}
